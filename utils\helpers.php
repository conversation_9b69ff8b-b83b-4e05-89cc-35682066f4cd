<?php
function m_log($arMsg)  
{  
	$stEntry=""; 	
	$arLogData['event_datetime']='['.date('D Y-m-d h:i:s A').'] [client '.$_SERVER['REMOTE_ADDR'].']';  	
	if(is_array($arMsg))  
	{  		
		foreach($arMsg as $msg)  
			$stEntry.=$arLogData['event_datetime']." ".$msg."\r\n";  
	}  
	else  
	{  
		$stEntry.=$arLogData['event_datetime']." ".$arMsg."\r\n";  
	}  
	$stCurLogFileName='log_'.date('Ymd').'.txt';  
	$fHandler=fopen(LOG_PATH.$stCurLogFileName,'a+');  
	fwrite($fHandler,$stEntry);  
	fclose($fHandler);  
} 

function pre($data){
	echo "<pre>";
	print_r($data);
	echo "</pre>";
}
function does_url_exists($url) {
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_exec($ch);
    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if ($code == 200) {
        $status = true;
    } else {
        $status = false;
    }
    curl_close($ch);
    return $status;
}
function find_sku($filename, $sku) {

    $f = fopen($filename, "r");
    $result = false;
    while ($col = fgetcsv($f)) {
    	
        if ($col[0] == $sku) {

            $result = $col[0];
            break;
        }
    }
    // pre($result); exit;
    fclose($f);
    return $result;
}

?>