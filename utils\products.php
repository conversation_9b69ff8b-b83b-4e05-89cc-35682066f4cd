<?php


function getWpProducts(){
	
	$url = WP_URL."products?per_page=40&consumer_key=".COMSUMER_KEY."&consumer_secret=".COMSUMER_SECRET;
	
	$curl_response =  wp_curl($url);
	if($curl_response){
		$result = json_decode($curl_response,true);			
		return $result;
	} else {
		return false;
	}	
}

function getWPCategories(){
	
	$url =  WP_URL."products/categories?&consumer_key=".COMSUMER_KEY."&consumer_secret=".COMSUMER_SECRET;	
	$curl_response =  wp_curl($url);
	if($curl_response){
		$result = json_decode($curl_response,true);			
		return $result;
	} else {
		return false;
	}	
}
function getWooProductVariants($product_id){
	$url =  WP_URL."products/".$product_id."/variations?consumer_key=".COMSUMER_KEY."&consumer_secret=".COMSUMER_SECRET;	
	$curl_response =  wp_curl($url);
	if($curl_response){
		$result = json_decode($curl_response,true);			
		return $result;
	} else {
		return false;
	}
}

function getProductsBySku($limit,$page){
	$url = "catalog/products?include_fields=sku,name,custom_url,categories&limit=".$limit."&page=".$page;	
	$curl_response =  curl($url,'GET');

	if($curl_response){
		$result = json_decode($curl_response);			
		return $result->data;
	} else {
		return false;
	}	
}

function getProductsAsCategory($limit,$page){
	$url = "catalog/products?categories:in=15,22&categories:not_in=3,1500,2598,1,29,25&include_fields=categories&limit=".$limit."&page=".$page;	
	$curl_response =  curl($url,'GET');

	if($curl_response){
		$result = json_decode($curl_response);			
		return $result->data;
	} else {
		return false;
	}	
}

function allProductCountAsCat(){
	$url = "catalog/products?categories:in=15,22&categories:not_in=3,1500,2598,1,29,25&include_fields=categories";	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response);	
	if(isset($result->meta)){			
		return $result->meta->pagination->total;
	} else {
		return false;
	}	
}
function getCustom_fields($id){
	$url = "catalog/products/".$id."/custom-fields";	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response,true);	

	if($result['data']){				
		return $result['data'];
	} else {
		return false;
	}
}

function getProduct($sku){
	$url = "catalog/products?sku=".$sku;	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response,true);	

	if($result['data']){				
		return $result['data'][0];
	} else {
		return false;
	}	
}
function getvariants($id){
	$url = "catalog/products/".$id."/variants";	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response,true);	
	
	if($result['data']){				
		return $result['data'];
	} else {
		return false;
	}	
}
function getImage($id){
	$url = "catalog/products/".$id."/images";	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response,true);	
	
	if($result['data'][0]){				
		return $result['data'][0];
	} else {
		return false;
	}	
}
function deleteProduct($id){
	$url = "catalog/products/".$id;	
	$curl_response =  curl($url,'DELETE');	
	if($curl_response){				
		return true;
	} else {
		return false;
	}	
}


function createProduct($data){
	$url = "catalog/products";	
	$curl_response =  curl($url,'POST',$data);
	$result = json_decode($curl_response);	
	m_log($curl_response);
	if(isset($result->data)){			
		return $result->data->id;
	} else {
		return false;
	}	
}

function createVariantsData($id,$data){
	$url = "catalog/products/".$id."/variants";
	
	$curl_response =  curl($url,'POST',$data);
	$result = json_decode($curl_response);			
	if(isset($result->data)){			
		return $result->data;
	} else {
		return false;
	}
}

function getProductOptions($id){
	$url = "catalog/products/".$id."/options";	
	$curl_response =  curl($url,'GET');
	// pre($curl_response);
	$result = json_decode($curl_response,true);		
	// pre($result);
	if(isset($result['data'])){			
		return $result['data'];
	} else {
		return false;
	}
}

function createVariants($id,$data){
	$url = "catalog/products/".$id."/options";	
	$curl_response =  curl($url,'POST',$data);
	$result = json_decode($curl_response);			
	// pre($result);
	if(isset($result->data)){			
		return $result->data;
	} else {
		return false;
	}
}

function updateLocationInventory($data){
	$url = "inventory/adjustments/absolute";	
	$curl_response =  curl($url,'PUT',$data);
	$result = json_decode($curl_response,true);
	pre($result);
	if(isset($result['transaction_id'])){			
		return $result['transaction_id'];
	} else {
		return false;
	}
}
function getLocations(){
	$url = "inventory/locations";	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response,true);	
	if(isset($result['data'])){			
		return $result['data'];
	} else {
		return false;
	}

}

// function createModifiers($id,$data){
// 	$url = API_URL_V3."catalog/products/".$id."/modifiers";	
// 	$curl_response =  curl($url,'POST',ACCESS_TOKEN_V2,$data);
// 	// pre($curl_response);exit;	
// 	$result = json_decode($curl_response,true);

// 	if(isset($result['data'])){		
// 		m_log("modifier created - ".$id);
// 		return $result['data']['id'];
// 	} else {
// 		m_log("modifier failed - ".$curl_response);
// 		return false;
// 	}
// }

function ProductChannelAssignment($product_id, $channel_ids) {
	$url = "catalog/products/channel-assignments";

    // Convert comma-separated string to array if needed
	if (!is_array($channel_ids)) {
		$channel_ids = explode(',', $channel_ids);
	}

    // Build the data payload
	$data = [];
	foreach ($channel_ids as $channel_id) {
		$data[] = [
			"product_id" => $product_id,
			"channel_id" => (int)trim($channel_id)
		];
	}

	$curl_response = curl($url, 'PUT', $data);
	$result = json_decode($curl_response, true);
	pre($result);
	return $result ?: false;
}




function ProductChannelAssignment1($product_id, $channel_id){
	$url = API_URL."catalog/products/channel-assignments";

	$data = [
		[
			"product_id" => $product_id,
			"channel_id" => $channel_id
		]
	];

	$curl_response =  curl($url,'PUT',$data);
	// pre($curl_response);exit;
	$result = json_decode($curl_response,true);
	if(isset($result)){
		return $result;
	} else {
		return false;
	}
}



/**
 * Get a specific WordPress product variation
 *
 * @param int $product_id WordPress product ID
 * @param int $variant_id WordPress variation ID
 * @return array|false Returns variation data on success, false on failure
 */
function getWooProductVariation($product_id, $variant_id) {
	$url = WP_URL . "products/$product_id/variations/$variant_id?consumer_key=" . COMSUMER_KEY . "&consumer_secret=" . COMSUMER_SECRET;

	$curl_response = wp_curl($url);
	if ($curl_response) {
		$result = json_decode($curl_response, true);
		return $result;
	} else {
		return false;
	}
}

/**
 * Get all variants for a product with their current wholesale pricing
 *
 * @param int $product_id WordPress product ID
 * @return array|false Returns array of variants with pricing data, false on failure
 */
function getProductVariantsWithPricing($product_id) {
	$variants = getWooProductVariants($product_id);

	if (!$variants) {
		return false;
	}

	$variants_with_pricing = [];

	foreach ($variants as $variant) {
		$variant_data = [
			'id' => $variant['id'],
			'sku' => $variant['sku'],
			'name' => $variant['name'],
			'regular_price' => $variant['regular_price'],
			'wholesale_pricing' => []
		];

		// Extract wholesale pricing from meta data
		foreach ($variant['meta_data'] as $meta) {
			if ($meta['key'] === 'wholesale_multi_user_pricing') {
				$wholesale_pricing = $meta['value'];

				// Wholesale (role ID 453, price list ID 2)
				if (isset($wholesale_pricing[453]) && isset($wholesale_pricing[453][$variant['id']])) {
					$variant_data['wholesale_pricing']['wholesale'] = [
						'price_list_id' => 2,
						'price' => $wholesale_pricing[453][$variant['id']]['wholesaleprice']
					];
				}

				// Canada Wholesale (role ID 455, price list ID 3)
				if (isset($wholesale_pricing[455]) && isset($wholesale_pricing[455][$variant['id']])) {
					$variant_data['wholesale_pricing']['canada_wholesale'] = [
						'price_list_id' => 3,
						'price' => $wholesale_pricing[455][$variant['id']]['wholesaleprice']
					];
				}
				break;
			}
		}

		$variants_with_pricing[] = $variant_data;
	}

	return $variants_with_pricing;
}

// ============================================================================
// BIGCOMMERCE PRICE LIST FUNCTIONS
// ============================================================================

/**
 * Get all BigCommerce price lists
 *
 * @return array|false Returns array of price lists on success, false on failure
 */
function getBigCommercePriceLists() {
	$url = "pricelists";
	$curl_response = curl($url, 'GET');
	$result = json_decode($curl_response, true);

	if (isset($result['data'])) {
		return $result['data'];
	} else {
		return false;
	}
}

/**
 * Create a new BigCommerce price list
 *
 * @param array $data Price list data
 * @return int|false Returns price list ID on success, false on failure
 */
function createBigCommercePriceList($data) {
	$url = "pricelists";
	$curl_response = curl($url, 'POST', $data);
	$result = json_decode($curl_response, true);

	if (isset($result['data']['id'])) {
		return $result['data']['id'];
	} else {
		return false;
	}
}

/**
 * Get a specific BigCommerce price list
 *
 * @param int $price_list_id Price list ID
 * @return array|false Returns price list data on success, false on failure
 */
function getBigCommercePriceList($price_list_id) {
	$url = "pricelists/$price_list_id";
	$curl_response = curl($url, 'GET');
	$result = json_decode($curl_response, true);

	if (isset($result['data'])) {
		return $result['data'];
	} else {
		return false;
	}
}

/**
 * Update a BigCommerce price list
 *
 * @param int $price_list_id Price list ID
 * @param array $data Update data
 * @return bool Returns true on success, false on failure
 */
function updateBigCommercePriceList($price_list_id, $data) {
	$url = "pricelists/$price_list_id";
	$curl_response = curl($url, 'PUT', $data);

	if ($curl_response) {
		return true;
	} else {
		return false;
	}
}

/**
 * Delete a BigCommerce price list
 *
 * @param int $price_list_id Price list ID
 * @return bool Returns true on success, false on failure
 */
function deleteBigCommercePriceList($price_list_id) {
	$url = "pricelists/$price_list_id";
	$curl_response = curl($url, 'DELETE');

	if ($curl_response) {
		return true;
	} else {
		return false;
	}
}

/**
 * Get price records for a specific price list
 *
 * @param int $price_list_id Price list ID
 * @return array|false Returns array of price records on success, false on failure
 */
function getBigCommercePriceRecords($price_list_id) {
	$url = "pricelists/$price_list_id/records";
	$curl_response = curl($url, 'GET');
	$result = json_decode($curl_response, true);

	if (isset($result['data'])) {
		return $result['data'];
	} else {
		return false;
	}
}

/**
 * Create/Update price records for a BigCommerce price list (Upsert)
 *
 * @param int $price_list_id Price list ID
 * @param array $records Array of price records
 * @return array|false Returns created/updated records on success, false on failure
 */
function upsertBigCommercePriceRecords($price_list_id, $records) {
	$url = "pricelists/$price_list_id/records";
	$curl_response = curl($url, 'PUT', $records);
	$result = json_decode($curl_response, true);

	// Debug: Log the full API response to understand what's happening
	echo "DEBUG - Price List API Response: " . $curl_response . "\n";
	echo "DEBUG - Parsed Result: " . json_encode($result) . "\n";

	// Check for successful response - BigCommerce might return different structures
	if (isset($result['data']) ||
		(isset($result['meta']) && isset($result['meta']['saved_records']) && $result['meta']['saved_records'] > 0) ||
		(is_array($result) && !isset($result['errors']))) {

		echo "DEBUG - Success: Price records created/updated\n";
		return $result['data'] ?? $result ?? true;
	} else {
		// Log errors for debugging
		echo "DEBUG - API Error Response: " . json_encode($result) . "\n";
		if (isset($result['errors'])) {
			echo "DEBUG - Specific Errors: " . json_encode($result['errors']) . "\n";
			error_log("BigCommerce API Errors: " . json_encode($result['errors']));
		}
		if (isset($result['title'])) {
			echo "DEBUG - Error Title: " . $result['title'] . "\n";
			error_log("BigCommerce API Error: " . $result['title']);
		}
		return false;
	}
}

/**
 * Assign BigCommerce price list to variant with specific price
 *
 * @param int $price_list_id BigCommerce price list ID
 * @param int $variant_id BigCommerce variant ID
 * @param float $price Price to assign
 * @param string $currency Currency code (default: INR)
 * @return bool Returns true on success, false on failure
 */
function assignBigCommercePriceToVariant($price_list_id, $variant_id, $price, $currency = 'INR') {
	$price_record = [
		[
			'variant_id' => $variant_id,
			'price' => (float)$price,
			'currency' => $currency
		]
	];

	echo "DEBUG - Sending price record: " . json_encode($price_record) . "\n";

	$result = upsertBigCommercePriceRecords($price_list_id, $price_record);

	if ($result !== false) {
		return true;
	} else {
		return false;
	}
}

/**
 * Batch assign prices to multiple variants in BigCommerce price list
 *
 * @param int $price_list_id BigCommerce price list ID
 * @param array $variant_prices Array of variant_id => price pairs
 * @param string $currency Currency code (default: INR)
 * @return array|false Returns result summary on success, false on failure
 */
function batchAssignBigCommercePrices($price_list_id, $variant_prices, $currency = 'INR') {
	$price_records = [];

	foreach ($variant_prices as $variant_id => $price) {
		$price_records[] = [
			'variant_id' => $variant_id,
			'price' => (float)$price,
			'currency' => $currency
		];
	}

	$result = upsertBigCommercePriceRecords($price_list_id, $price_records);

	if ($result !== false) {
		return [
			'success' => true,
			'total_records' => count($price_records),
			'price_list_id' => $price_list_id,
			'data' => $result
		];
	} else {
		return false;
	}
}



/**
 * Get BigCommerce variant ID by SKU
 *
 * @param string $sku Product/variant SKU
 * @return int|false Returns variant ID on success, false on failure
 */
function getBigCommerceVariantBySku($sku) {
	$url = "catalog/variants?sku=" . urlencode($sku);
	$curl_response = curl($url, 'GET');
	$result = json_decode($curl_response, true);

	if (isset($result['data']) && !empty($result['data'])) {
		return $result['data'][0]['id'];
	} else {
		return false;
	}
}


/**
 * Generate all possible variant combinations and create them in BigCommerce
 *
 * @param int $product_id BigCommerce product ID
 * @param string $mainSKU Base SKU for generating variant SKUs
 * @param array $woo_variants WordPress variants data for matching prices
 * @return array Returns array of created variants with their IDs
 */
function generateAllVariantsAndCreate($product_id, $mainSKU, $woo_variants = []) {
    $options = getProductOptions($product_id);

    if (!$options) {
        echo "No options found for product $product_id\n";
        return [];
    }

    // Step 1: Extract all option value IDs grouped by option_id
    $optionGroups = [];
    foreach ($options as $option) {
        $option_id = $option['id'];
        foreach ($option['option_values'] as $value) {
            $optionGroups[$option_id][] = [
                'option_id' => $option_id,
                'option_value_id' => $value['id'],
                'label' => $value['label'],
                'option_name' => $option['display_name']
            ];
        }
    }

    // Step 2: Generate Cartesian product of all option value combinations
    $combinations = cartesianProduct(array_values($optionGroups));

    echo "Generated " . count($combinations) . " variant combinations\n";

    // Step 3: Format data and create variants
    $created_variants = [];

    foreach ($combinations as $combo) {
        $skuParts = [];
        $variant = [
            'option_values' => [],
            'price' => 0.00,
            'inventory_level' => 0
        ];

        // Build option values and SKU parts
        foreach ($combo as $opt) {
            $variant['option_values'][] = [
                'option_id' => $opt['option_id'],
                'id' => $opt['option_value_id']
            ];

            // Add first 3 letters of label (uppercase) for SKU
            $label = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $opt['label']), 0, 3));
            $skuParts[] = $label;
        }

        $variant['sku'] = $mainSKU . '-' . implode('-', $skuParts);

        // Try to match with WordPress variant data for pricing and inventory
        $matched_woo_variant = findMatchingWooVariant($combo, $woo_variants);
        if ($matched_woo_variant) {
            $variant['price'] = (float)$matched_woo_variant['price'];
            $variant['inventory_level'] = $matched_woo_variant['stock_quantity'] ?? 0;
            $variant['weight'] = (float)$matched_woo_variant['weight'];

            // Add dimensions if available
            if (!empty($matched_woo_variant['dimensions'])) {
                if (!empty($matched_woo_variant['dimensions']['width']))
                    $variant['width'] = (float)$matched_woo_variant['dimensions']['width'];
                if (!empty($matched_woo_variant['dimensions']['height']))
                    $variant['height'] = (float)$matched_woo_variant['dimensions']['height'];
                if (!empty($matched_woo_variant['dimensions']['length']))
                    $variant['depth'] = (float)$matched_woo_variant['dimensions']['length'];
            }

            // Add image if available
            if (!empty($matched_woo_variant['image']['src'])) {
                $variant['image_url'] = $matched_woo_variant['image']['src'];
            }
        }

        // Create the variant in BigCommerce
        echo "Creating variant: " . $variant['sku'] . "\n";
        $result = createVariantsData($product_id, $variant);

        if ($result) {
            $created_variants[] = [
                'bc_variant_id' => $result->id,
                'sku' => $variant['sku'],
                'option_combination' => $combo,
                'woo_variant' => $matched_woo_variant,
                'bc_variant_data' => $result
            ];
            echo "✓ Created variant: " . $variant['sku'] . " (ID: " . $result->id . ")\n";
        } else {
            echo "✗ Failed to create variant: " . $variant['sku'] . "\n";
        }
    }

    return $created_variants;
}

/**
 * Helper function to generate Cartesian product of arrays
 */
function cartesianProduct($arrays) {
    $result = [[]];
    foreach ($arrays as $propertyValues) {
        $tmp = [];
        foreach ($result as $resultItem) {
            foreach ($propertyValues as $propertyValue) {
                $tmp[] = array_merge($resultItem, [$propertyValue]);
            }
        }
        $result = $tmp;
    }
    return $result;
}

/**
 * Find matching WordPress variant based on option combination
 */
function findMatchingWooVariant($combo, $woo_variants) {
    foreach ($woo_variants as $woo_variant) {
        $matches = 0;
        $total_attributes = count($woo_variant['attributes']);

        if ($total_attributes === 0) continue;

        foreach ($woo_variant['attributes'] as $attr) {
            foreach ($combo as $opt) {
                if (strcasecmp($opt['option_name'], $attr['name']) === 0 &&
                    strcasecmp($opt['label'], $attr['option']) === 0) {
                    $matches++;
                    break;
                }
            }
        }

        // If all attributes match, this is our variant
        if ($matches === $total_attributes && $matches === count($combo)) {
            return $woo_variant;
        }
    }

    return null;
}

/**
 * Dynamically create product options based on WordPress product attributes
 *
 * @param int $product_id BigCommerce product ID
 * @param array $wp_attributes WordPress product attributes
 * @return array Returns array of created options with their IDs
 */
function createDynamicProductOptions($product_id, $wp_attributes) {
    $created_options = [];

    foreach ($wp_attributes as $attr) {
        $option_data = [
            'product_id' => $product_id,
            'display_name' => $attr['name'],
            'type' => 'rectangles', // Can be 'rectangles', 'radio_buttons', 'dropdown', etc.
            'option_values' => []
        ];

        // Create option values
        foreach ($attr['options'] as $index => $option_value) {
            $option_data['option_values'][] = [
                'label' => $option_value,
                'is_default' => $index === 0
            ];
        }

        echo "Creating option: " . $attr['name'] . " with " . count($attr['options']) . " values\n";

        $result = createVariants($product_id, $option_data);

        if ($result) {
            $created_options[] = [
                'wp_attribute' => $attr,
                'bc_option_id' => $result->id,
                'bc_option_data' => $result
            ];
            echo "✓ Created option: " . $attr['name'] . " (ID: " . $result->id . ")\n";
        } else {
            echo "✗ Failed to create option: " . $attr['name'] . "\n";
        }
    }

    return $created_options;
}

/**
 * Sync pricing data for created variants to BigCommerce price lists
 *
 * @param array $created_variants Array of created variants from generateAllVariantsAndCreate
 * @param array $price_list_config Price list configuration
 * @return array Returns sync results
 */
function syncVariantPricingToPriceLists($created_variants, $price_list_config = null) {
    if (!$price_list_config) {
        // Default price list configuration
        $price_list_config = [
            'wholesale' => [
                'bc_price_list_id' => 2,
                'name' => 'Wholesale',
                'wp_role_id' => 453
            ],
            'canada_wholesale' => [
                'bc_price_list_id' => 3,
                'name' => 'Canada Wholesale',
                'wp_role_id' => 455
            ]
        ];
    }

    $sync_results = [];

    foreach ($price_list_config as $type => $config) {
        echo "\nSyncing $type pricing (BC Price List ID: {$config['bc_price_list_id']})...\n";

        $price_records = [];
        $sync_count = 0;

        foreach ($created_variants as $variant) {
            if (!$variant['woo_variant']) continue;

            // Extract wholesale pricing from WordPress variant meta data
            $wholesale_price = null;
            foreach ($variant['woo_variant']['meta_data'] as $meta) {
                if ($meta['key'] === 'wholesale_multi_user_pricing') {
                    $wholesale_pricing = $meta['value'];

                    if (isset($wholesale_pricing[$config['wp_role_id']]) &&
                        isset($wholesale_pricing[$config['wp_role_id']][$variant['woo_variant']['id']])) {
                        $wholesale_price = $wholesale_pricing[$config['wp_role_id']][$variant['woo_variant']['id']]['wholesaleprice'];
                        break;
                    }
                }
            }

            if ($wholesale_price && $wholesale_price > 0) {
                $price_records[$variant['bc_variant_id']] = $wholesale_price;
                echo "- Variant {$variant['sku']} -> BC Variant {$variant['bc_variant_id']}: \${$wholesale_price}\n";
                $sync_count++;
            }
        }

        if (!empty($price_records)) {
            $batch_result = batchAssignBigCommercePrices($config['bc_price_list_id'], $price_records);

            if ($batch_result && isset($batch_result['success']) && $batch_result['success']) {
                echo "✓ Successfully synced $sync_count prices for $type\n";
                $sync_results[$type] = [
                    'success' => true,
                    'count' => $sync_count,
                    'price_list_id' => $config['bc_price_list_id']
                ];
            } else {
                echo "✗ Failed to sync prices for $type\n";
                $sync_results[$type] = [
                    'success' => false,
                    'count' => 0,
                    'error' => 'Batch assignment failed'
                ];
            }
        } else {
            echo "No pricing data found for $type\n";
            $sync_results[$type] = [
                'success' => true,
                'count' => 0,
                'message' => 'No data to sync'
            ];
        }
    }

    return $sync_results;
}

/**
 * Complete dynamic import workflow for a single WordPress product
 *
 * @param array $wp_product WordPress product data
 * @param array $options Import options and configuration
 * @return array Returns complete import results
 */
function dynamicProductImport($wp_product, $options = []) {
    $import_results = [
        'wp_product_id' => $wp_product['id'],
        'wp_product_name' => $wp_product['name'],
        'bc_product_id' => null,
        'created_options' => [],
        'created_variants' => [],
        'pricing_sync_results' => [],
        'success' => false,
        'errors' => []
    ];

    try {
        echo "\n" . str_repeat("=", 80) . "\n";
        echo "DYNAMIC IMPORT: {$wp_product['name']} (WP ID: {$wp_product['id']})\n";
        echo str_repeat("=", 80) . "\n";

        // Step 1: Create base product
        echo "1. Creating base product...\n";
        $product_data = buildBaseProductData($wp_product);
        $bc_product_id = createProduct($product_data);

        if (!$bc_product_id) {
            throw new Exception("Failed to create base product");
        }

        $import_results['bc_product_id'] = $bc_product_id;
        echo "✓ Created base product (BC ID: $bc_product_id)\n";

        // Step 2: Create dynamic options
        echo "\n2. Creating dynamic product options...\n";
        if (!empty($wp_product['attributes'])) {
            $created_options = createDynamicProductOptions($bc_product_id, $wp_product['attributes']);
            $import_results['created_options'] = $created_options;
        } else {
            echo "No attributes found, skipping option creation\n";
        }

        // Step 3: Get WordPress variants
        echo "\n3. Getting WordPress variants...\n";
        $woo_variants = getWooProductVariants($wp_product['id']);
        if ($woo_variants) {
            echo "✓ Found " . count($woo_variants) . " WordPress variants\n";
        } else {
            echo "No WordPress variants found\n";
            $woo_variants = [];
        }

        // Step 4: Generate and create all variants
        echo "\n4. Generating and creating variants...\n";
        $created_variants = generateAllVariantsAndCreate($bc_product_id, $wp_product['sku'], $woo_variants);
        $import_results['created_variants'] = $created_variants;

        // Step 5: Sync pricing to price lists
        echo "\n5. Syncing pricing to BigCommerce price lists...\n";
        if (!empty($created_variants)) {
            $pricing_sync_results = syncVariantPricingToPriceLists($created_variants, $options['price_list_config'] ?? null);
            $import_results['pricing_sync_results'] = $pricing_sync_results;
        } else {
            echo "No variants created, skipping pricing sync\n";
        }

        $import_results['success'] = true;
        echo "\n✓ IMPORT COMPLETED SUCCESSFULLY\n";

    } catch (Exception $e) {
        $import_results['errors'][] = $e->getMessage();
        echo "\n✗ IMPORT FAILED: " . $e->getMessage() . "\n";
    }

    return $import_results;
}

/**
 * Build base product data from WordPress product
 */
function buildBaseProductData($wp_product) {
    $product_data = [
        'sku' => $wp_product['sku'],
        'type' => 'physical',
        'name' => $wp_product['name'],
        'price' => $wp_product['price'],
        'weight' => $wp_product['weight'] ? $wp_product['weight'] : 0,
        'description' => $wp_product['short_description'] . '' . $wp_product['description'],
        'is_visible' => $wp_product['status'] == 'publish'
    ];

    // Add dimensions if available
    if (isset($wp_product['dimensions']['length']) && $wp_product['dimensions']['length'] != "") {
        $product_data['length'] = $wp_product['dimensions']['length'];
    }
    if (isset($wp_product['dimensions']['depth']) && $wp_product['dimensions']['depth'] != "") {
        $product_data['depth'] = $wp_product['dimensions']['depth'];
    }
    if (isset($wp_product['dimensions']['height']) && $wp_product['dimensions']['height'] != "") {
        $product_data['height'] = $wp_product['dimensions']['height'];
    }

    // Add categories
    $category_array = [];
    foreach ($wp_product['categories'] as $cat) {
        $cat_id = getCategoriesByName($cat['name']);
        if ($cat_id) {
            $category_array[] = $cat_id;
        }
    }
    $product_data['categories'] = $category_array;

    // Add images
    if (!empty($wp_product['images'])) {
        $img_data = [];
        foreach ($wp_product['images'] as $index => $image) {
            $img_data[] = [
                "image_url" => $image['src'], // Use actual image URL instead of placeholder
                "is_thumbnail" => $index === 0,
            ];
        }
        if (!empty($img_data)) {
            $product_data['images'] = $img_data;
        }
    }

    return $product_data;
}