<?php


function getWpProducts(){
	
	$url = WP_URL."products?per_page=100&consumer_key=".COMSUMER_KEY."&consumer_secret=".COMSUMER_SECRET;
	$curl_response =  wp_curl($url);
	if($curl_response){
		$result = json_decode($curl_response,true);	
		return $result;
	} else {
		return false;
	}	
}

function getWPCategories(){
	
	$url =  WP_URL."products/categories?&consumer_key=".COMSUMER_KEY."&consumer_secret=".COMSUMER_SECRET;	
	$curl_response =  wp_curl($url);
	if($curl_response){
		$result = json_decode($curl_response,true);			
		return $result;
	} else {
		return false;
	}	
}
function getWooProductVariants($product_id){
	$url =  WP_URL."products/".$product_id."/variations?per_page=50&consumer_key=".COMSUMER_KEY."&consumer_secret=".COMSUMER_SECRET;	
	$curl_response =  wp_curl($url);
	if($curl_response){
		$result = json_decode($curl_response,true);			
		return $result;
	} else {
		return false;
	}
}

function getProductsBySku($limit,$page){
	$url = "catalog/products?include_fields=sku,name,custom_url,categories&limit=".$limit."&page=".$page;	
	$curl_response =  curl($url,'GET');

	if($curl_response){
		$result = json_decode($curl_response);			
		return $result->data;
	} else {
		return false;
	}	
}

function getProductsAsCategory($limit,$page){
	$url = "catalog/products?categories:in=15,22&categories:not_in=3,1500,2598,1,29,25&include_fields=categories&limit=".$limit."&page=".$page;	
	$curl_response =  curl($url,'GET');

	if($curl_response){
		$result = json_decode($curl_response);			
		return $result->data;
	} else {
		return false;
	}	
}

function allProductCountAsCat(){
	$url = "catalog/products?categories:in=15,22&categories:not_in=3,1500,2598,1,29,25&include_fields=categories";	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response);	
	if(isset($result->meta)){			
		return $result->meta->pagination->total;
	} else {
		return false;
	}	
}
function getCustom_fields($id){
	$url = "catalog/products/".$id."/custom-fields";	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response,true);	

	if($result['data']){				
		return $result['data'];
	} else {
		return false;
	}
}

function getProduct($sku){
	$url = "catalog/products?sku=".$sku;	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response,true);	

	if($result['data']){				
		return $result['data'][0];
	} else {
		return false;
	}	
}
function getvariants($id){
	$url = "catalog/products/".$id."/variants";	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response,true);	
	
	if($result['data']){				
		return $result['data'];
	} else {
		return false;
	}	
}
function getImage($id){
	$url = "catalog/products/".$id."/images";	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response,true);	
	
	if($result['data'][0]){				
		return $result['data'][0];
	} else {
		return false;
	}	
}
function deleteProduct($id){
	$url = "catalog/products/".$id;	
	$curl_response =  curl($url,'DELETE');	
	if($curl_response){				
		return true;
	} else {
		return false;
	}	
}


function createProduct($data){
	$url = "catalog/products";	
	$curl_response =  curl($url,'POST',$data);
	$result = json_decode($curl_response);	
	// m_log($curl_response);
	if(isset($result->data)){			
		return $result->data->id;
	} else {
		return false;
	}	
}

function createVariantsData($id,$data){
	$url = "catalog/products/".$id."/variants";

	$curl_response =  curl($url,'POST',$data);
	$result = json_decode($curl_response);
	if(isset($result->data)){
		return $result->data;
	} else {
		return false;
	}
}

function getProductOptions($id){
	$url = "catalog/products/".$id."/options";	
	$curl_response =  curl($url,'GET');
	// pre($curl_response);
	$result = json_decode($curl_response,true);		
	// pre($result);
	if(isset($result['data'])){			
		return $result['data'];
	} else {
		return false;
	}
}

function createVariants($id,$data){
	$url = "catalog/products/".$id."/options";	
	$curl_response =  curl($url,'POST',$data);
	$result = json_decode($curl_response);			
	// pre($result);
	if(isset($result->data)){			
		return $result->data;
	} else {
		return false;
	}
}

function updateLocationInventory($data){
	$url = "inventory/adjustments/absolute";	
	$curl_response =  curl($url,'PUT',$data);
	$result = json_decode($curl_response,true);
	if(isset($result['transaction_id'])){			
		return $result['transaction_id'];
	} else {
		return false;
	}
}
function getLocations(){
	$url = "inventory/locations";	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response,true);	
	if(isset($result['data'])){			
		return $result['data'];
	} else {
		return false;
	}

}

// function createModifiers($id,$data){
// 	$url = API_URL_V3."catalog/products/".$id."/modifiers";	
// 	$curl_response =  curl($url,'POST',ACCESS_TOKEN_V2,$data);
// 	// pre($curl_response);exit;	
// 	$result = json_decode($curl_response,true);

// 	if(isset($result['data'])){		
// 		m_log("modifier created - ".$id);
// 		return $result['data']['id'];
// 	} else {
// 		m_log("modifier failed - ".$curl_response);
// 		return false;
// 	}
// }

function ProductChannelAssignment($product_id, $channel_ids) {
	$url = "catalog/products/channel-assignments";

    // Convert comma-separated string to array if needed
	if (!is_array($channel_ids)) {
		$channel_ids = explode(',', $channel_ids);
	}

    // Build the data payload
	$data = [];
	foreach ($channel_ids as $channel_id) {
		$data[] = [
			"product_id" => $product_id,
			"channel_id" => (int)trim($channel_id)
		];
	}

	$curl_response = curl($url, 'PUT', $data);
	$result = json_decode($curl_response, true);
	// pre($result);
	return $result ?: false;
}




function ProductChannelAssignment1($product_id, $channel_id){
	$url = API_URL."catalog/products/channel-assignments";

	$data = [
		[
			"product_id" => $product_id,
			"channel_id" => $channel_id
		]
	];

	$curl_response =  curl($url,'PUT',$data);
	// pre($curl_response);exit;
	$result = json_decode($curl_response,true);
	if(isset($result)){
		return $result;
	} else {
		return false;
	}
}



/**
 * Get a specific WordPress product variation
 *
 * @param int $product_id WordPress product ID
 * @param int $variant_id WordPress variation ID
 * @return array|false Returns variation data on success, false on failure
 */
function getWooProductVariation($product_id, $variant_id) {
	$url = WP_URL . "products/$product_id/variations/$variant_id?consumer_key=" . COMSUMER_KEY . "&consumer_secret=" . COMSUMER_SECRET;

	$curl_response = wp_curl($url);
	if ($curl_response) {
		$result = json_decode($curl_response, true);
		return $result;
	} else {
		return false;
	}
}

/**
 * Get all variants for a product with their current wholesale pricing
 *
 * @param int $product_id WordPress product ID
 * @return array|false Returns array of variants with pricing data, false on failure
 */
function getProductVariantsWithPricing($product_id) {
	$variants = getWooProductVariants($product_id);

	if (!$variants) {
		return false;
	}

	$variants_with_pricing = [];

	foreach ($variants as $variant) {
		$variant_data = [
			'id' => $variant['id'],
			'sku' => $variant['sku'],
			'name' => $variant['name'],
			'regular_price' => $variant['regular_price'],
			'wholesale_pricing' => []
		];

		// Extract wholesale pricing from meta data
		foreach ($variant['meta_data'] as $meta) {
			if ($meta['key'] === 'wholesale_multi_user_pricing') {
				$wholesale_pricing = $meta['value'];

				// Wholesale (role ID 453, price list ID 2)
				if (isset($wholesale_pricing[453]) && isset($wholesale_pricing[453][$variant['id']])) {
					$variant_data['wholesale_pricing']['wholesale'] = [
						'price_list_id' => 2,
						'price' => $wholesale_pricing[453][$variant['id']]['wholesaleprice']
					];
				}

				// Canada Wholesale (role ID 455, price list ID 3)
				if (isset($wholesale_pricing[455]) && isset($wholesale_pricing[455][$variant['id']])) {
					$variant_data['wholesale_pricing']['canada_wholesale'] = [
						'price_list_id' => 3,
						'price' => $wholesale_pricing[455][$variant['id']]['wholesaleprice']
					];
				}
				break;
			}
		}

		$variants_with_pricing[] = $variant_data;
	}

	return $variants_with_pricing;
}

// ============================================================================
// BIGCOMMERCE PRICE LIST FUNCTIONS
// ============================================================================

/**
 * Get all BigCommerce price lists
 *
 * @return array|false Returns array of price lists on success, false on failure
 */
function getBigCommercePriceLists() {
	$url = "pricelists";
	$curl_response = curl($url, 'GET');
	$result = json_decode($curl_response, true);

	if (isset($result['data'])) {
		return $result['data'];
	} else {
		return false;
	}
}

/**
 * Create a new BigCommerce price list
 *
 * @param array $data Price list data
 * @return int|false Returns price list ID on success, false on failure
 */
function createBigCommercePriceList($data) {
	$url = "pricelists";
	$curl_response = curl($url, 'POST', $data);
	$result = json_decode($curl_response, true);

	if (isset($result['data']['id'])) {
		return $result['data']['id'];
	} else {
		return false;
	}
}

/**
 * Get a specific BigCommerce price list
 *
 * @param int $price_list_id Price list ID
 * @return array|false Returns price list data on success, false on failure
 */
function getBigCommercePriceList($price_list_id) {
	$url = "pricelists/$price_list_id";
	$curl_response = curl($url, 'GET');
	$result = json_decode($curl_response, true);

	if (isset($result['data'])) {
		return $result['data'];
	} else {
		return false;
	}
}

/**
 * Update a BigCommerce price list
 *
 * @param int $price_list_id Price list ID
 * @param array $data Update data
 * @return bool Returns true on success, false on failure
 */
function updateBigCommercePriceList($price_list_id, $data) {
	$url = "pricelists/$price_list_id";
	$curl_response = curl($url, 'PUT', $data);

	if ($curl_response) {
		return true;
	} else {
		return false;
	}
}

/**
 * Delete a BigCommerce price list
 *
 * @param int $price_list_id Price list ID
 * @return bool Returns true on success, false on failure
 */
function deleteBigCommercePriceList($price_list_id) {
	$url = "pricelists/$price_list_id";
	$curl_response = curl($url, 'DELETE');

	if ($curl_response) {
		return true;
	} else {
		return false;
	}
}

/**
 * Get price records for a specific price list
 *
 * @param int $price_list_id Price list ID
 * @return array|false Returns array of price records on success, false on failure
 */
function getBigCommercePriceRecords($price_list_id) {
	$url = "pricelists/$price_list_id/records";
	$curl_response = curl($url, 'GET');
	$result = json_decode($curl_response, true);

	if (isset($result['data'])) {
		return $result['data'];
	} else {
		return false;
	}
}

/**
 * Create/Update price records for a BigCommerce price list (Upsert)
 *
 * @param int $price_list_id Price list ID
 * @param array $records Array of price records
 * @return array|false Returns created/updated records on success, false on failure
 */
function upsertBigCommercePriceRecords($price_list_id, $records) {
	$url = "pricelists/$price_list_id/records";
	$curl_response = curl($url, 'PUT', $records);
	$result = json_decode($curl_response, true);

	// Check for successful response - BigCommerce might return different structures
	if (isset($result['data']) ||
		(isset($result['meta']) && isset($result['meta']['saved_records']) && $result['meta']['saved_records'] > 0) ||
		(is_array($result) && !isset($result['errors']))) {

		return $result['data'] ?? $result ?? true;
	} else {
		// Log errors for debugging
		if (isset($result['errors'])) {
			error_log("BigCommerce API Errors: " . json_encode($result['errors']));
		}
		if (isset($result['title'])) {
			error_log("BigCommerce API Error: " . $result['title']);
		}
		return false;
	}
}

/**
 * Assign BigCommerce price list to variant with specific price
 *
 * @param int $price_list_id BigCommerce price list ID
 * @param int $variant_id BigCommerce variant ID
 * @param float $price Price to assign
 * @param string $currency Currency code (default: INR)
 * @return bool Returns true on success, false on failure
 */
function assignBigCommercePriceToVariant($price_list_id, $variant_id, $price, $currency = 'INR') {
	$price_record = [
		[
			'variant_id' => $variant_id,
			'price' => (float)$price,
			'currency' => $currency
		]
	];

	$result = upsertBigCommercePriceRecords($price_list_id, $price_record);

	if ($result !== false) {
		return true;
	} else {
		return false;
	}
}

/**
 * Batch assign prices to multiple variants in BigCommerce price list
 *
 * @param int $price_list_id BigCommerce price list ID
 * @param array $variant_prices Array of variant_id => price pairs
 * @param string $currency Currency code (default: INR)
 * @return array|false Returns result summary on success, false on failure
 */
function batchAssignBigCommercePrices($price_list_id, $variant_prices, $currency = 'INR') {
	$price_records = [];

	foreach ($variant_prices as $variant_id => $price) {
		$price_records[] = [
			'variant_id' => $variant_id,
			'price' => (float)$price,
			'currency' => $currency
		];
	}

	$result = upsertBigCommercePriceRecords($price_list_id, $price_records);

	if ($result !== false) {
		return [
			'success' => true,
			'total_records' => count($price_records),
			'price_list_id' => $price_list_id,
			'data' => $result
		];
	} else {
		return false;
	}
}



/**
 * Get BigCommerce variant ID by SKU
 *
 * @param string $sku Product/variant SKU
 * @return int|false Returns variant ID on success, false on failure
 */
function getBigCommerceVariantBySku($sku) {
	$url = "catalog/variants?sku=" . urlencode($sku);
	$curl_response = curl($url, 'GET');
	$result = json_decode($curl_response, true);

	if (isset($result['data']) && !empty($result['data'])) {
		return $result['data'][0]['id'];
	} else {
		return false;
	}
}


// Removed unused function: generateAllVariantsAndCreate()

// Removed unused helper functions

// Removed unused function: syncVariantPricingToPriceLists()

/**
 * Complete dynamic import workflow for a single WordPress product
 *
 * @param array $wp_product WordPress product data
 * @param array $options Import options and configuration
 * @return array Returns complete import results
 */
// Removed unused functions: dynamicProductImport() and buildBaseProductData()