<?php


function getWpProducts(){
	
	echo $url = WP_URL."products?per_page=40&consumer_key=".COMSUMER_KEY."&consumer_secret=".COMSUMER_SECRET;
	
	$curl_response =  wp_curl($url);
	if($curl_response){
		$result = json_decode($curl_response,true);			
		return $result;
	} else {
		return false;
	}	
}

function getWPCategories(){
	
	$url =  WP_URL."products/categories?&consumer_key=".COMSUMER_KEY."&consumer_secret=".COMSUMER_SECRET;	
	$curl_response =  wp_curl($url);
	if($curl_response){
		$result = json_decode($curl_response,true);			
		return $result;
	} else {
		return false;
	}	
}
function getWooProductVariants($product_id){
	echo $url =  WP_URL."products/".$product_id."/variations?consumer_key=".COMSUMER_KEY."&consumer_secret=".COMSUMER_SECRET;	
	$curl_response =  wp_curl($url);
	if($curl_response){
		$result = json_decode($curl_response,true);			
		return $result;
	} else {
		return false;
	}
}

function getProductsBySku($limit,$page){
	$url = "catalog/products?include_fields=sku,name,custom_url,categories&limit=".$limit."&page=".$page;	
	$curl_response =  curl($url,'GET');

	if($curl_response){
		$result = json_decode($curl_response);			
		return $result->data;
	} else {
		return false;
	}	
}

function getProductsAsCategory($limit,$page){
	$url = "catalog/products?categories:in=15,22&categories:not_in=3,1500,2598,1,29,25&include_fields=categories&limit=".$limit."&page=".$page;	
	$curl_response =  curl($url,'GET');

	if($curl_response){
		$result = json_decode($curl_response);			
		return $result->data;
	} else {
		return false;
	}	
}

function allProductCountAsCat(){
	$url = "catalog/products?categories:in=15,22&categories:not_in=3,1500,2598,1,29,25&include_fields=categories";	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response);	
	if(isset($result->meta)){			
		return $result->meta->pagination->total;
	} else {
		return false;
	}	
}
function getCustom_fields($id){
	$url = "catalog/products/".$id."/custom-fields";	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response,true);	

	if($result['data']){				
		return $result['data'];
	} else {
		return false;
	}
}

function getProduct($sku){
	$url = "catalog/products?sku=".$sku;	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response,true);	

	if($result['data']){				
		return $result['data'][0];
	} else {
		return false;
	}	
}
function getvariants($id){
	$url = "catalog/products/".$id."/variants";	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response,true);	
	
	if($result['data']){				
		return $result['data'];
	} else {
		return false;
	}	
}
function getImage($id){
	$url = "catalog/products/".$id."/images";	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response,true);	
	
	if($result['data'][0]){				
		return $result['data'][0];
	} else {
		return false;
	}	
}
function deleteProduct($id){
	$url = "catalog/products/".$id;	
	$curl_response =  curl($url,'DELETE');	
	if($curl_response){				
		return true;
	} else {
		return false;
	}	
}


function createProduct($data){
	$url = "catalog/products";	
	$curl_response =  curl($url,'POST',$data);
	$result = json_decode($curl_response);	
	m_log($curl_response);
	if(isset($result->data)){			
		return $result->data->id;
	} else {
		return false;
	}	
}

function createVariantsData($id,$data){
	$url = "catalog/products/".$id."/variants";
	
	$curl_response =  curl($url,'POST',$data);
	$result = json_decode($curl_response);			
	if(isset($result->data)){			
		return $result->data;
	} else {
		return false;
	}
}

function getProductOptions($id){
	$url = "catalog/products/".$id."/options";	
	$curl_response =  curl($url,'GET');
	// pre($curl_response);
	$result = json_decode($curl_response,true);		
	// pre($result);
	if(isset($result['data'])){			
		return $result['data'];
	} else {
		return false;
	}
}

function createVariants($id,$data){
	$url = "catalog/products/".$id."/options";	
	$curl_response =  curl($url,'POST',$data);
	$result = json_decode($curl_response);			
	// pre($result);
	if(isset($result->data)){			
		return $result->data;
	} else {
		return false;
	}
}

function updateLocationInventory($data){
	$url = "inventory/adjustments/absolute";	
	$curl_response =  curl($url,'PUT',$data);
	$result = json_decode($curl_response,true);
	pre($result);
	if(isset($result['transaction_id'])){			
		return $result['transaction_id'];
	} else {
		return false;
	}
}
function getLocations(){
	$url = "inventory/locations";	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response,true);	
	if(isset($result['data'])){			
		return $result['data'];
	} else {
		return false;
	}

}

// function createModifiers($id,$data){
// 	$url = API_URL_V3."catalog/products/".$id."/modifiers";	
// 	$curl_response =  curl($url,'POST',ACCESS_TOKEN_V2,$data);
// 	// pre($curl_response);exit;	
// 	$result = json_decode($curl_response,true);

// 	if(isset($result['data'])){		
// 		m_log("modifier created - ".$id);
// 		return $result['data']['id'];
// 	} else {
// 		m_log("modifier failed - ".$curl_response);
// 		return false;
// 	}
// }

function ProductChannelAssignment($product_id, $channel_ids) {
	$url = "catalog/products/channel-assignments";

    // Convert comma-separated string to array if needed
	if (!is_array($channel_ids)) {
		$channel_ids = explode(',', $channel_ids);
	}

    // Build the data payload
	$data = [];
	foreach ($channel_ids as $channel_id) {
		$data[] = [
			"product_id" => $product_id,
			"channel_id" => (int)trim($channel_id)
		];
	}

	$curl_response = curl($url, 'PUT', $data);
	$result = json_decode($curl_response, true);
	pre($result);
	return $result ?: false;
}




function ProductChannelAssignment1($product_id, $channel_id){
	$url = API_URL."catalog/products/channel-assignments";

	$data = [
		[
			"product_id" => $product_id,
			"channel_id" => $channel_id
		]
	];

	$curl_response =  curl($url,'PUT',$data);
	// pre($curl_response);exit;
	$result = json_decode($curl_response,true);
	if(isset($result)){
		return $result;
	} else {
		return false;
	}
}

/**
 * Assign price list pricing to a product variant using WordPress API
 *
 * @param int $product_id WordPress product ID
 * @param int $variant_id WordPress variation ID
 * @param int $price_list_id Price list ID (2 = Wholesale, 3 = Canada Wholesale)
 * @param float $price New wholesale price
 * @return bool|array Returns updated variation data on success, false on failure
 */
function assignVariantPriceList($product_id, $variant_id, $price_list_id, $price) {
	// Map price list IDs to WordPress user role IDs
	$price_list_mapping = [
		2 => 453,  // Wholesale
		3 => 455   // Canada Wholesale
	];

	// Validate price list ID
	if (!isset($price_list_mapping[$price_list_id])) {
		error_log("Invalid price list ID: $price_list_id. Valid IDs are 2 (Wholesale) or 3 (Canada Wholesale)");
		return false;
	}

	$wp_role_id = $price_list_mapping[$price_list_id];
	$price_list_name = ($price_list_id == 2) ? 'wholesale' : 'canada-wholesale';

	// First, get the current variation data
	$current_variation = getWooProductVariation($product_id, $variant_id);
	if (!$current_variation) {
		error_log("Failed to get variation data for product $product_id, variant $variant_id");
		return false;
	}

	// Extract current wholesale pricing meta data
	$wholesale_pricing = [];
	foreach ($current_variation['meta_data'] as $meta) {
		if ($meta['key'] === 'wholesale_multi_user_pricing') {
			$wholesale_pricing = $meta['value'];
			break;
		}
	}

	// Update the pricing for the specified price list
	if (!isset($wholesale_pricing[$wp_role_id])) {
		$wholesale_pricing[$wp_role_id] = [
			'slug' => $price_list_name,
			'discount_type' => 'fixed'
		];
	}

	$wholesale_pricing[$wp_role_id][$variant_id] = [
		'wholesaleprice' => number_format($price, 2, '.', ''),
		'qty' => '1',
		'step' => ''
	];

	// Prepare update data
	$update_data = [
		'meta_data' => [
			[
				'key' => 'wholesale_multi_user_pricing',
				'value' => $wholesale_pricing
			]
		]
	];

	// Update the variation
	$result = updateWooProductVariation($product_id, $variant_id, $update_data);

	if ($result) {
		error_log("Successfully assigned price list $price_list_id ($$price) to product $product_id, variant $variant_id");
		return $result;
	} else {
		error_log("Failed to assign price list $price_list_id to product $product_id, variant $variant_id");
		return false;
	}
}

/**
 * Get a specific WordPress product variation
 *
 * @param int $product_id WordPress product ID
 * @param int $variant_id WordPress variation ID
 * @return array|false Returns variation data on success, false on failure
 */
function getWooProductVariation($product_id, $variant_id) {
	$url = WP_URL . "products/$product_id/variations/$variant_id?consumer_key=" . COMSUMER_KEY . "&consumer_secret=" . COMSUMER_SECRET;

	$curl_response = wp_curl($url);
	if ($curl_response) {
		$result = json_decode($curl_response, true);
		return $result;
	} else {
		return false;
	}
}

/**
 * Update a WordPress product variation
 *
 * @param int $product_id WordPress product ID
 * @param int $variant_id WordPress variation ID
 * @param array $data Update data
 * @return array|false Returns updated variation data on success, false on failure
 */
function updateWooProductVariation($product_id, $variant_id, $data) {
	$url = WP_URL . "products/$product_id/variations/$variant_id?consumer_key=" . COMSUMER_KEY . "&consumer_secret=" . COMSUMER_SECRET;

	$curl_response = wp_curl_put($url, $data);
	if ($curl_response) {
		$result = json_decode($curl_response, true);
		return $result;
	} else {
		return false;
	}
}

/**
 * WordPress cURL function for PUT requests
 *
 * @param string $url The URL to send the request to
 * @param array $data The data to send
 * @return string|false Returns response on success, false on failure
 */
function wp_curl_put($url, $data) {
	$curl = curl_init();

	$headers = [
		'Accept: application/json',
		'Content-Type: application/json'
	];

	curl_setopt_array($curl, array(
		CURLOPT_URL => $url,
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 30,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_SSL_VERIFYPEER => false,
		CURLOPT_CUSTOMREQUEST => 'PUT',
		CURLOPT_POSTFIELDS => json_encode($data),
		CURLOPT_HTTPHEADER => $headers,
	));

	$response = curl_exec($curl);
	$err = curl_error($curl);
	curl_close($curl);

	if ($err) {
		error_log("cURL Error: $err");
		return false;
	} else {
		return $response;
	}
}

/**
 * Batch assign price lists to multiple variants
 *
 * @param array $assignments Array of assignments with keys: product_id, variant_id, price_list_id, price
 * @return array Results array with success/failure status for each assignment
 */
function batchAssignVariantPriceLists($assignments) {
	$results = [];
	$success_count = 0;
	$total_count = count($assignments);

	foreach ($assignments as $index => $assignment) {
		// Validate required fields
		if (!isset($assignment['product_id']) || !isset($assignment['variant_id']) ||
			!isset($assignment['price_list_id']) || !isset($assignment['price'])) {
			$results[$index] = [
				'success' => false,
				'error' => 'Missing required fields',
				'assignment' => $assignment
			];
			continue;
		}

		$result = assignVariantPriceList(
			$assignment['product_id'],
			$assignment['variant_id'],
			$assignment['price_list_id'],
			$assignment['price']
		);

		if ($result) {
			$success_count++;
			$results[$index] = [
				'success' => true,
				'data' => $result,
				'assignment' => $assignment
			];
		} else {
			$results[$index] = [
				'success' => false,
				'error' => 'Assignment failed',
				'assignment' => $assignment
			];
		}
	}

	return [
		'results' => $results,
		'summary' => [
			'total' => $total_count,
			'successful' => $success_count,
			'failed' => $total_count - $success_count,
			'success_rate' => $total_count > 0 ? round(($success_count / $total_count) * 100, 2) : 0
		]
	];
}

/**
 * Get all variants for a product with their current wholesale pricing
 *
 * @param int $product_id WordPress product ID
 * @return array|false Returns array of variants with pricing data, false on failure
 */
function getProductVariantsWithPricing($product_id) {
	$variants = getWooProductVariants($product_id);

	if (!$variants) {
		return false;
	}

	$variants_with_pricing = [];

	foreach ($variants as $variant) {
		$variant_data = [
			'id' => $variant['id'],
			'sku' => $variant['sku'],
			'name' => $variant['name'],
			'regular_price' => $variant['regular_price'],
			'wholesale_pricing' => []
		];

		// Extract wholesale pricing from meta data
		foreach ($variant['meta_data'] as $meta) {
			if ($meta['key'] === 'wholesale_multi_user_pricing') {
				$wholesale_pricing = $meta['value'];

				// Wholesale (role ID 453, price list ID 2)
				if (isset($wholesale_pricing[453]) && isset($wholesale_pricing[453][$variant['id']])) {
					$variant_data['wholesale_pricing']['wholesale'] = [
						'price_list_id' => 2,
						'price' => $wholesale_pricing[453][$variant['id']]['wholesaleprice']
					];
				}

				// Canada Wholesale (role ID 455, price list ID 3)
				if (isset($wholesale_pricing[455]) && isset($wholesale_pricing[455][$variant['id']])) {
					$variant_data['wholesale_pricing']['canada_wholesale'] = [
						'price_list_id' => 3,
						'price' => $wholesale_pricing[455][$variant['id']]['wholesaleprice']
					];
				}
				break;
			}
		}

		$variants_with_pricing[] = $variant_data;
	}

	return $variants_with_pricing;
}


// function generateAllVariantsAndCreate($product_id,$mainSKU) {
//     $options = getProductOptions($product_id);

//     // Step 1: Extract all option value IDs grouped by option_id
//     $optionGroups = [];
//     foreach ($options as $option) {
//         $option_id = $option['id'];
//         foreach ($option['option_values'] as $value) {
//             $optionGroups[$option_id][] = [
//                 'option_id' => $option_id,
//                 'option_value_id' => $value['id'],
//                 'label' => $value['label']
//             ];
//         }
//     }

//     // Step 2: Generate Cartesian product of all option value combinations
//     $combinations = cartesianProduct(array_values($optionGroups));

//     // Step 3: Format data and create variants
//     $variants = [];
//    foreach ($combinations as $combo) {
//     $skuParts = [];

//     $variant = [
//         'option_values' => [],
//         'price' => 0.00,
//         'inventory_level' => 100
//     ];

//     foreach ($combo as $opt) {
//         $variant['option_values'][] = [
//             'option_id' => $opt['option_id'],
//             'id' => $opt['option_value_id']
//         ];

//         // Add first 3 letters of label (uppercase)
//         $label = strtoupper(substr(preg_replace('/[^A-Za-z0-9]/', '', $opt['label']), 0, 3));
//         $skuParts[] = $label;
//     }

//     $variant['sku'] = $mainSKU . '-' . implode('-', $skuParts);
//     $variants[] = $variant;
// }


//     // Step 4: Send to BigCommerce (in batches if large)
//     // BigCommerce API supports up to 50 variants at once
//    foreach ($variants as $variant) {
//     $result = createVariantsData($product_id, $variant);
//     if (!$result) {
//         echo "Failed to create variant:\n";
//         // print_r($variant);
//     }
// }
// }

// // Helper function to generate Cartesian product
// function cartesianProduct($arrays) {
//     $result = [[]];
//     foreach ($arrays as $propertyValues) {
//         $tmp = [];
//         foreach ($result as $resultItem) {
//             foreach ($propertyValues as $propertyValue) {
//                 $tmp[] = array_merge($resultItem, [$propertyValue]);
//             }
//         }
//         $result = $tmp;
//     }
//     return $result;
// }



?>