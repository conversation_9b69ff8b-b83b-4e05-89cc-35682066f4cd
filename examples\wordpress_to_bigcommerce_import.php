<?php
/**
 * WordPress to BigCommerce Import Example
 * 
 * This example demonstrates how to get product data from WordPress
 * and import it into BigCommerce price lists.
 */

include("../config/config.php");
include("../config/price_list_config.php");

echo "=== WordPress to BigCommerce Import Example ===\n\n";

// Configuration
$wordpress_product_id = 104733; // Bad Ace Soap product

// Step 1: Get WordPress product variants with wholesale pricing
echo "1. Getting WordPress product data...\n";
$wp_variants = getProductVariantsWithPricing($wordpress_product_id);

if (!$wp_variants) {
    echo "✗ Failed to get WordPress variants\n";
    exit(1);
}

echo "✓ Found " . count($wp_variants) . " WordPress variants\n";

// Display the WordPress data we'll be importing
foreach ($wp_variants as $variant) {
    echo "\nVariant ID: " . $variant['id'] . "\n";
    echo "- SKU: " . $variant['sku'] . "\n";
    echo "- Name: " . $variant['name'] . "\n";
    echo "- Regular Price: $" . $variant['regular_price'] . "\n";
    
    if (!empty($variant['wholesale_pricing'])) {
        echo "- Wholesale Pricing to Import:\n";
        
        if (isset($variant['wholesale_pricing']['wholesale'])) {
            echo "  * Wholesale: $" . $variant['wholesale_pricing']['wholesale']['price'] . "\n";
        }
        
        if (isset($variant['wholesale_pricing']['canada_wholesale'])) {
            echo "  * Canada Wholesale: $" . $variant['wholesale_pricing']['canada_wholesale']['price'] . "\n";
        }
    } else {
        echo "- No wholesale pricing found\n";
    }
}

echo "\n" . str_repeat("-", 60) . "\n\n";

// Step 2: Get existing BigCommerce price list IDs
echo "2. Using existing BigCommerce price lists...\n";

$wholesale_price_list_id = getBigCommercePriceListId('wholesale');
$canada_wholesale_price_list_id = getBigCommercePriceListId('canada_wholesale');

echo "✓ Wholesale Price List ID: $wholesale_price_list_id\n";
echo "✓ Canada Wholesale Price List ID: $canada_wholesale_price_list_id\n";

// Verify the price lists exist
$wholesale_list = getBigCommercePriceList($wholesale_price_list_id);
$canada_list = getBigCommercePriceList($canada_wholesale_price_list_id);

if (!$wholesale_list) {
    echo "✗ Wholesale price list (ID: $wholesale_price_list_id) not found!\n";
    echo "  Please check your configuration in config/price_list_config.php\n";
}

if (!$canada_list) {
    echo "✗ Canada Wholesale price list (ID: $canada_wholesale_price_list_id) not found!\n";
    echo "  Please check your configuration in config/price_list_config.php\n";
}

echo "\n" . str_repeat("-", 60) . "\n\n";

// Step 3: Map WordPress variants to BigCommerce variants
echo "3. Mapping WordPress variants to BigCommerce...\n";

$import_data = [];

foreach ($wp_variants as $wp_variant) {
    if (empty($wp_variant['wholesale_pricing'])) {
        echo "Skipping variant " . $wp_variant['id'] . " - no wholesale pricing\n";
        continue;
    }
    
    $sku = $wp_variant['sku'];
    $bc_variant_id = getBigCommerceVariantBySku($sku);
    
    if ($bc_variant_id) {
        echo "✓ Mapped WP variant " . $wp_variant['id'] . " to BC variant $bc_variant_id (SKU: $sku)\n";
        
        $import_data[] = [
            'wp_variant' => $wp_variant,
            'bc_variant_id' => $bc_variant_id,
            'sku' => $sku
        ];
    } else {
        echo "✗ No BigCommerce variant found for SKU: $sku\n";
    }
}

if (empty($import_data)) {
    echo "No variants to import - exiting\n";
    exit(0);
}

echo "\n" . str_repeat("-", 60) . "\n\n";

// Step 4: Import wholesale pricing to BigCommerce
echo "4. Importing wholesale pricing to BigCommerce...\n";

// Import Wholesale pricing
if ($wholesale_price_list_id) {
    echo "\nImporting Wholesale pricing (Price List ID: $wholesale_price_list_id)...\n";
    
    $wholesale_prices = [];
    foreach ($import_data as $item) {
        $wp_variant = $item['wp_variant'];
        $bc_variant_id = $item['bc_variant_id'];
        
        if (isset($wp_variant['wholesale_pricing']['wholesale'])) {
            $price = floatval($wp_variant['wholesale_pricing']['wholesale']['price']);
            $wholesale_prices[$bc_variant_id] = $price;
            
            echo "- BC Variant $bc_variant_id: $$price\n";
        }
    }
    
    if (!empty($wholesale_prices)) {
        $result = batchAssignBigCommercePrices($wholesale_price_list_id, $wholesale_prices);
        
        if ($result) {
            echo "✓ Successfully imported " . count($wholesale_prices) . " wholesale prices\n";
        } else {
            echo "✗ Failed to import wholesale prices\n";
        }
    }
}

// Import Canada Wholesale pricing
if ($canada_wholesale_price_list_id) {
    echo "\nImporting Canada Wholesale pricing (Price List ID: $canada_wholesale_price_list_id)...\n";
    
    $canada_prices = [];
    foreach ($import_data as $item) {
        $wp_variant = $item['wp_variant'];
        $bc_variant_id = $item['bc_variant_id'];
        
        if (isset($wp_variant['wholesale_pricing']['canada_wholesale'])) {
            $price = floatval($wp_variant['wholesale_pricing']['canada_wholesale']['price']);
            $canada_prices[$bc_variant_id] = $price;
            
            echo "- BC Variant $bc_variant_id: $$price\n";
        }
    }
    
    if (!empty($canada_prices)) {
        $result = batchAssignBigCommercePrices($canada_wholesale_price_list_id, $canada_prices);
        
        if ($result) {
            echo "✓ Successfully imported " . count($canada_prices) . " Canada wholesale prices\n";
        } else {
            echo "✗ Failed to import Canada wholesale prices\n";
        }
    }
}

echo "\n" . str_repeat("-", 60) . "\n\n";

// Step 5: Verify the import
echo "5. Verifying imported data...\n";

if ($wholesale_price_list_id) {
    echo "\nWholesale Price List (ID: $wholesale_price_list_id):\n";
    $records = getBigCommercePriceRecords($wholesale_price_list_id);
    
    if ($records) {
        foreach ($records as $record) {
            echo "- Variant " . $record['variant_id'] . ": $" . $record['price'] . "\n";
        }
    } else {
        echo "No records found\n";
    }
}

if ($canada_wholesale_price_list_id) {
    echo "\nCanada Wholesale Price List (ID: $canada_wholesale_price_list_id):\n";
    $records = getBigCommercePriceRecords($canada_wholesale_price_list_id);
    
    if ($records) {
        foreach ($records as $record) {
            echo "- Variant " . $record['variant_id'] . ": $" . $record['price'] . "\n";
        }
    } else {
        echo "No records found\n";
    }
}

echo "\n=== Import Complete ===\n";

echo "\nSummary:\n";
echo "- WordPress variants processed: " . count($wp_variants) . "\n";
echo "- BigCommerce variants mapped: " . count($import_data) . "\n";
echo "- Wholesale price list ID: " . ($wholesale_price_list_id ?: 'Not created') . "\n";
echo "- Canada wholesale price list ID: " . ($canada_wholesale_price_list_id ?: 'Not created') . "\n";

echo "\nNext Steps:\n";
echo "1. Check your BigCommerce admin panel to see the price lists\n";
echo "2. Assign the price lists to customer groups\n";
echo "3. Test the pricing on your BigCommerce storefront\n";

?>
