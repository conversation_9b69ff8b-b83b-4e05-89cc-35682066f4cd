<?php
/**
 * Test Enhanced Import System
 * 
 * This script tests the enhanced import functionality with price list synchronization
 */

include("../config/config.php");

echo "=== TESTING ENHANCED IMPORT SYSTEM ===\n\n";

// Test Configuration
$test_product_id = 104733; // Your test product ID

echo "Testing with WordPress Product ID: $test_product_id\n\n";

// Step 1: Test WordPress product retrieval
echo "1. Testing WordPress product retrieval...\n";
$wp_products = getWpProducts();

if (!$wp_products) {
    die("✗ Failed to fetch WordPress products\n");
}

$test_product = null;
foreach ($wp_products as $product) {
    if ($product['id'] == $test_product_id) {
        $test_product = $product;
        break;
    }
}

if (!$test_product) {
    die("✗ Test product with ID $test_product_id not found\n");
}

echo "✓ Found test product: {$test_product['name']}\n";

// Step 2: Test WordPress variants retrieval
echo "\n2. Testing WordPress variants retrieval...\n";
$woo_variants = getWooProductVariants($test_product_id);

if (!$woo_variants) {
    die("✗ Failed to fetch WordPress variants\n");
}

echo "✓ Found " . count($woo_variants) . " WordPress variants\n";

// Step 3: Test wholesale pricing extraction
echo "\n3. Testing wholesale pricing extraction...\n";
$variants_with_pricing = 0;
$wholesale_prices = [];
$canada_wholesale_prices = [];

foreach ($woo_variants as $variant) {
    $has_pricing = false;
    
    foreach ($variant['meta_data'] as $meta) {
        if ($meta['key'] === 'wholesale_multi_user_pricing') {
            $wholesale_pricing = $meta['value'];
            
            // Check wholesale pricing (role ID 453)
            if (isset($wholesale_pricing[453]) && isset($wholesale_pricing[453][$variant['id']])) {
                $price = $wholesale_pricing[453][$variant['id']]['wholesaleprice'];
                if ($price && $price > 0) {
                    $wholesale_prices[$variant['sku']] = $price;
                    $has_pricing = true;
                }
            }
            
            // Check Canada wholesale pricing (role ID 455)
            if (isset($wholesale_pricing[455]) && isset($wholesale_pricing[455][$variant['id']])) {
                $price = $wholesale_pricing[455][$variant['id']]['wholesaleprice'];
                if ($price && $price > 0) {
                    $canada_wholesale_prices[$variant['sku']] = $price;
                    $has_pricing = true;
                }
            }
            break;
        }
    }
    
    if ($has_pricing) {
        $variants_with_pricing++;
    }
}

echo "✓ Found pricing data for $variants_with_pricing variants\n";
echo "  - Wholesale prices: " . count($wholesale_prices) . "\n";
echo "  - Canada wholesale prices: " . count($canada_wholesale_prices) . "\n";

// Step 4: Display pricing data
if (!empty($wholesale_prices)) {
    echo "\nWholesale Pricing Data:\n";
    foreach ($wholesale_prices as $sku => $price) {
        echo "  - $sku: \$$price\n";
    }
}

if (!empty($canada_wholesale_prices)) {
    echo "\nCanada Wholesale Pricing Data:\n";
    foreach ($canada_wholesale_prices as $sku => $price) {
        echo "  - $sku: \$$price\n";
    }
}

// Step 5: Test BigCommerce price list verification
echo "\n4. Testing BigCommerce price list verification...\n";

$price_lists = [
    'wholesale' => 2,
    'canada_wholesale' => 3
];

foreach ($price_lists as $name => $id) {
    $price_list = getBigCommercePriceList($id);
    if ($price_list) {
        echo "✓ Verified price list '$name' (ID: $id)\n";
    } else {
        echo "✗ Failed to verify price list '$name' (ID: $id)\n";
    }
}

// Step 6: Test price sync functions
echo "\n5. Testing price sync functions...\n";

// Test single price assignment (dry run simulation)
echo "Testing assignBigCommercePriceToVariant function...\n";
if (function_exists('assignBigCommercePriceToVariant')) {
    echo "✓ assignBigCommercePriceToVariant function exists\n";
} else {
    echo "✗ assignBigCommercePriceToVariant function not found\n";
}

// Test batch price assignment
echo "Testing batchAssignBigCommercePrices function...\n";
if (function_exists('batchAssignBigCommercePrices')) {
    echo "✓ batchAssignBigCommercePrices function exists\n";
} else {
    echo "✗ batchAssignBigCommercePrices function not found\n";
}

echo "\n" . str_repeat("=", 60) . "\n";
echo "TEST SUMMARY\n";
echo str_repeat("=", 60) . "\n";
echo "WordPress Product: ✓ Found\n";
echo "WordPress Variants: ✓ " . count($woo_variants) . " found\n";
echo "Variants with Pricing: ✓ $variants_with_pricing found\n";
echo "Wholesale Prices: ✓ " . count($wholesale_prices) . " found\n";
echo "Canada Wholesale Prices: ✓ " . count($canada_wholesale_prices) . " found\n";
echo "Price List Functions: ✓ Available\n";

echo "\nREADY FOR IMPORT!\n";
echo "You can now run the enhanced import.php script to:\n";
echo "1. Create the product and variants in BigCommerce\n";
echo "2. Automatically sync wholesale pricing to price lists\n";
echo "3. Get detailed pricing sync statistics\n";

echo "\n=== TEST COMPLETE ===\n";

?>
