<?php
include("../config/config.php");

echo "=== TESTING BIGCOMMERCE PRICE LISTS ===\n\n";

// Test if price lists exist
$price_lists_to_test = [
    2 => 'Wholesale',
    3 => 'Canada Wholesale'
];

foreach ($price_lists_to_test as $id => $name) {
    echo "Testing Price List: $name (ID: $id)\n";
    
    $price_list = getBigCommercePriceList($id);
    
    if ($price_list) {
        echo "✓ Price List '$name' exists\n";
        echo "  - ID: {$price_list['id']}\n";
        echo "  - Name: {$price_list['name']}\n";
        echo "  - Active: " . ($price_list['active'] ? 'Yes' : 'No') . "\n";
    } else {
        echo "✗ Price List '$name' (ID: $id) not found\n";
    }
    echo "\n";
}

// Test currency settings
echo "=== TESTING CURRENCY SETTINGS ===\n\n";

// Get store currencies
$url = "currencies";
$curl_response = curl($url, 'GET');
$result = json_decode($curl_response, true);

if (isset($result['data'])) {
    echo "Available Currencies:\n";
    foreach ($result['data'] as $currency) {
        $default = $currency['is_default'] ? ' (DEFAULT)' : '';
        echo "- {$currency['currency_code']}: {$currency['name']}{$default}\n";
    }
} else {
    echo "Failed to fetch currencies\n";
}

echo "\n=== TEST COMPLETE ===\n";
?>
