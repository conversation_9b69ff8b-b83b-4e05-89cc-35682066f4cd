
<?php
/**
 * WordPress to BigCommerce Category Synchronization System
 *
 * This script provides comprehensive category creation and mapping functionality
 * for the WordPress to BigCommerce import system. It handles:
 * - Automatic category creation for missing categories
 * - Parent-child hierarchy mapping
 * - Category name conflict resolution
 * - Comprehensive error handling and logging
 */

include "../config/config.php";

/**
 * Synchronize WordPress categories to BigCommerce with proper hierarchy
 *
 * @return array Returns synchronization results with statistics
 */
function synchronizeWordPressCategoriesToBigCommerce() {
    $sync_stats = [
        'total_wp_categories' => 0,
        'existing_categories' => 0,
        'created_categories' => 0,
        'failed_categories' => 0,
        'parent_categories_created' => 0,
        'child_categories_created' => 0,
        'errors' => []
    ];

    // Get WordPress categories
    $wpCategories = getWPCategories();
    if (!$wpCategories) {
        m_log("ERROR: Failed to fetch WordPress categories");
        $sync_stats['errors'][] = "Failed to fetch WordPress categories";
        return $sync_stats;
    }

    $sync_stats['total_wp_categories'] = count($wpCategories);
    m_log("Starting category synchronization - Found " . count($wpCategories) . " WordPress categories");

    // Create category mapping array
    $categoryMapping = [];

    // Step 1: Create all parent categories first (categories with parent = 0)
    m_log("STEP 1: Creating parent categories");
    foreach ($wpCategories as $cat) {
        if ($cat['parent'] == 0) {
            $result = createOrMapCategory($cat, 0, $categoryMapping);
            if ($result['success']) {
                $categoryMapping[$cat['id']] = $result['bc_category_id'];
                if ($result['created']) {
                    $sync_stats['created_categories']++;
                    $sync_stats['parent_categories_created']++;
                } else {
                    $sync_stats['existing_categories']++;
                }
            } else {
                $sync_stats['failed_categories']++;
                $sync_stats['errors'][] = "Failed to create parent category: {$cat['name']} - " . $result['error'];
            }
        }
    }

    // Step 2: Create child categories (categories with parent != 0)
    m_log("STEP 2: Creating child categories");
    $max_iterations = 5; // Prevent infinite loops
    $iteration = 0;

    do {
        $iteration++;
        $categories_created_this_iteration = 0;

        foreach ($wpCategories as $cat) {
            if ($cat['parent'] != 0 && !isset($categoryMapping[$cat['id']])) {
                // Check if parent category has been mapped
                if (isset($categoryMapping[$cat['parent']])) {
                    $parent_bc_id = $categoryMapping[$cat['parent']];
                    $result = createOrMapCategory($cat, $parent_bc_id, $categoryMapping);

                    if ($result['success']) {
                        $categoryMapping[$cat['id']] = $result['bc_category_id'];
                        $categories_created_this_iteration++;

                        if ($result['created']) {
                            $sync_stats['created_categories']++;
                            $sync_stats['child_categories_created']++;
                        } else {
                            $sync_stats['existing_categories']++;
                        }
                    } else {
                        $sync_stats['failed_categories']++;
                        $sync_stats['errors'][] = "Failed to create child category: {$cat['name']} - " . $result['error'];
                    }
                }
            }
        }

        m_log("Iteration $iteration: Created $categories_created_this_iteration categories");

    } while ($categories_created_this_iteration > 0 && $iteration < $max_iterations);

    // Step 3: Handle orphaned categories (categories whose parents couldn't be created)
    foreach ($wpCategories as $cat) {
        if ($cat['parent'] != 0 && !isset($categoryMapping[$cat['id']])) {
            m_log("WARNING: Orphaned category found - {$cat['name']} (WP ID: {$cat['id']}, Parent: {$cat['parent']})");

            // Try to create as root category
            $result = createOrMapCategory($cat, 0, $categoryMapping);
            if ($result['success']) {
                $categoryMapping[$cat['id']] = $result['bc_category_id'];
                if ($result['created']) {
                    $sync_stats['created_categories']++;
                } else {
                    $sync_stats['existing_categories']++;
                }
                m_log("Created orphaned category as root: {$cat['name']} => BC ID: {$result['bc_category_id']}");
            } else {
                $sync_stats['failed_categories']++;
                $sync_stats['errors'][] = "Failed to create orphaned category: {$cat['name']} - " . $result['error'];
            }
        }
    }

    $sync_stats['category_mapping'] = $categoryMapping;
    return $sync_stats;
}

/**
 * Create or map a single category to BigCommerce
 *
 * @param array $wp_category WordPress category data
 * @param int $parent_bc_id BigCommerce parent category ID (0 for root)
 * @param array $categoryMapping Current category mapping for reference
 * @return array Returns result with success status, BC category ID, and creation status
 */
function createOrMapCategory($wp_category, $parent_bc_id, &$categoryMapping) {
    $result = [
        'success' => false,
        'bc_category_id' => null,
        'created' => false,
        'error' => null
    ];

    // Check if category already exists in BigCommerce
    $existing_bc_id = getCategoriesByName($wp_category['name']);

    if ($existing_bc_id) {
        // Category already exists
        $result['success'] = true;
        $result['bc_category_id'] = $existing_bc_id;
        $result['created'] = false;

        $parent_info = $parent_bc_id > 0 ? " under parent ID: $parent_bc_id" : " as root category";
        m_log("Category already exists: {$wp_category['name']} => BC ID: $existing_bc_id$parent_info");

        return $result;
    }

    // Category doesn't exist, create it
    $category_data = [[
        'name' => $wp_category['name'],
        'parent_id' => $parent_bc_id,
        'tree_id' => 1,
        'is_visible' => true,
        'description' => $wp_category['description'] ?? '',
        'sort_order' => $wp_category['menu_order'] ?? 0
    ]];

    try {
        $bc_category_id = createCategory($category_data);

        if ($bc_category_id) {
            $result['success'] = true;
            $result['bc_category_id'] = $bc_category_id;
            $result['created'] = true;

            $parent_info = $parent_bc_id > 0 ? " under parent ID: $parent_bc_id" : " as root category";
            m_log("Created category: {$wp_category['name']} => BC ID: $bc_category_id$parent_info");

        } else {
            $result['error'] = "createCategory() returned false";
            m_log("ERROR: Failed to create category: {$wp_category['name']} - createCategory() returned false");
        }

    } catch (Exception $e) {
        $result['error'] = $e->getMessage();
        m_log("ERROR: Exception creating category {$wp_category['name']}: " . $e->getMessage());
    }

    return $result;
}

/**
 * Validate category mapping and check for missing categories
 *
 * @param array $categoryMapping Category mapping array
 * @return array Returns validation results
 */
function validateCategoryMapping($categoryMapping) {
    $validation_results = [
        'total_mapped' => count($categoryMapping),
        'valid_mappings' => 0,
        'invalid_mappings' => 0,
        'missing_categories' => []
    ];

    foreach ($categoryMapping as $wp_id => $bc_id) {
        // Simple validation based on BC ID
        if ($bc_id > 0) {
            $validation_results['valid_mappings']++;
        } else {
            $validation_results['invalid_mappings']++;
            $validation_results['missing_categories'][] = $wp_id;
        }
    }

    return $validation_results;
}

// Main execution
echo "=== WordPress to BigCommerce Category Synchronization ===\n\n";

// Run category synchronization
$sync_results = synchronizeWordPressCategoriesToBigCommerce();

if (!$sync_results || !is_array($sync_results)) {
    echo "✗ Category synchronization failed - could not fetch WordPress categories\n";
    exit(1);
}

// Display results
echo "\n" . str_repeat("=", 60) . "\n";
echo "CATEGORY SYNCHRONIZATION SUMMARY\n";
echo str_repeat("=", 60) . "\n";
echo "Total WordPress Categories: {$sync_results['total_wp_categories']}\n";
echo "Existing Categories (Already in BC): {$sync_results['existing_categories']}\n";
echo "New Categories Created: {$sync_results['created_categories']}\n";
echo "  - Parent Categories: {$sync_results['parent_categories_created']}\n";
echo "  - Child Categories: {$sync_results['child_categories_created']}\n";
echo "Failed Categories: {$sync_results['failed_categories']}\n";

if (!empty($sync_results['errors'])) {
    echo "\nERRORS:\n";
    foreach ($sync_results['errors'] as $error) {
        echo "  ✗ $error\n";
    }
}

$success_rate = $sync_results['total_wp_categories'] > 0 ?
    round((($sync_results['existing_categories'] + $sync_results['created_categories']) / $sync_results['total_wp_categories']) * 100, 1) : 0;

echo "\nSuccess Rate: {$success_rate}%\n";

// Display category mapping
if (!empty($sync_results['category_mapping'])) {
    echo "\nCATEGORY MAPPING (WordPress ID => BigCommerce ID):\n";
    foreach ($sync_results['category_mapping'] as $wp_id => $bc_id) {
        echo "  WP: $wp_id => BC: $bc_id\n";
    }
}

echo "\n" . str_repeat("=", 60) . "\n";

// Log final summary
m_log("Category synchronization completed - Success rate: {$success_rate}%");
m_log("Categories created: {$sync_results['created_categories']}, Existing: {$sync_results['existing_categories']}, Failed: {$sync_results['failed_categories']}");

echo "Category synchronization completed successfully!\n";
echo "You can now run the product import with confidence that all categories exist in BigCommerce.\n";

exit;
