
<?php
include("../config/config.php");
$wpCategories = getWPCategories();
if (!$wpCategories) {
	echo "Failed to fetch categories.\n";
	return;
}

    // $idMap = []; // WordPress ID => BigCommerce ID

    // // Step 1: Create all parent categories first
    // foreach ($wpCategories as $cat) {
    // 	if ($cat['parent'] == 0) {
    // 		$data =[[
    // 			'name' => $cat['name'],
    // 			'parent_id' => 0,
    // 			'tree_id'=>1,
    // 			'is_visible' => true
    // 		]];

    // 		$bcId = createCategory($data);
    // 		m_log("catgeory_id - ".$bcId." Category Name - ".$cat['name']);
    // 		if ($bcId) {
    // 			$idMap[$cat['id']] = $bcId;
    // 			echo "Created parent category: {$cat['name']} => BC ID: $bcId\n";
    // 		}
    // 	}
    // }



    // Step 2: Create all child categories

foreach ($wpCategories as $cat) {
	if ($cat['parent'] != 0) {
        // Step 1: Get WP parent name
		$wpParent = null;
		foreach ($wpCategories as $c) {
			if ($c['id'] == $cat['parent']) {
				$wpParent = $c['name'];
				break;
			}
		}

		if (!$wpParent) {
			echo "Skipping {$cat['name']} — parent not found.\n";
			continue;
		}

        // Step 2: Get BigCommerce parent ID
		$parentBCId = getCategoriesByName($wpParent);

		if (!$parentBCId) {
			echo "Skipping {$cat['name']} — parent '{$wpParent}' not found in BigCommerce.\n";
			continue;
		}

        // Step 3: Create child category
		$data = [[
			'name' => $cat['name'],
			'parent_id' => $parentBCId,
			'tree_id' => 1,
			'is_visible' => true
		]];
		// pre($data);

		$bcId = createCategory($data);
		m_log("catgeory_id - ".$bcId." Category Name - ".$cat['name']);

		if ($bcId) {
			echo "Created child category: {$cat['name']} under {$wpParent} => BC ID: $bcId\n";
		}
	}
}

exit;
