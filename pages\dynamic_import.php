<?php
/**
 * Dynamic Product Import System
 * 
 * This script provides a fully automated workflow for importing WordPress products
 * to BigCommerce with dynamic variant creation and price list integration.
 * 
 * Features:
 * - Dynamic variant option creation
 * - Automatic variant generation from all possible combinations
 * - Automatic price list integration
 * - Comprehensive error handling and logging
 */

include("../config/config.php");

// Configuration
$import_config = [
    'price_list_config' => [
        'wholesale' => [
            'bc_price_list_id' => 2,
            'name' => 'Wholesale',
            'wp_role_id' => 453
        ],
        'canada_wholesale' => [
            'bc_price_list_id' => 3,
            'name' => 'Canada Wholesale', 
            'wp_role_id' => 455
        ]
    ],
    'batch_size' => 1, // Number of products to process at once
    'specific_product_id' => 104733, // Set to null to process all products
    'dry_run' => false // Set to true to simulate without actually creating anything
];

echo "=== DYNAMIC PRODUCT IMPORT SYSTEM ===\n";
echo "Configuration:\n";
echo "- Batch Size: {$import_config['batch_size']}\n";
echo "- Specific Product: " . ($import_config['specific_product_id'] ? $import_config['specific_product_id'] : 'All products') . "\n";
echo "- Dry Run: " . ($import_config['dry_run'] ? 'Yes' : 'No') . "\n";
echo "- Price Lists: " . count($import_config['price_list_config']) . " configured\n";
echo "\n";

// Get WordPress products
echo "Fetching WordPress products...\n";
$wp_products = getWpProducts();

if (!$wp_products) {
    die("Failed to fetch WordPress products\n");
}

echo "✓ Found " . count($wp_products) . " WordPress products\n\n";

// Filter products if specific product ID is set
if ($import_config['specific_product_id']) {
    $wp_products = array_filter($wp_products, function($product) use ($import_config) {
        return $product['id'] == $import_config['specific_product_id'];
    });
    
    if (empty($wp_products)) {
        die("Product with ID {$import_config['specific_product_id']} not found\n");
    }
    
    echo "Filtered to specific product: " . reset($wp_products)['name'] . "\n\n";
}

// Process products
$processed_count = 0;
$success_count = 0;
$error_count = 0;
$all_results = [];

foreach ($wp_products as $wp_product) {
    if ($processed_count >= $import_config['batch_size']) {
        echo "Reached batch limit of {$import_config['batch_size']} products\n";
        break;
    }
    
    $processed_count++;
    
    if ($import_config['dry_run']) {
        echo "\n[DRY RUN] Would process: {$wp_product['name']} (ID: {$wp_product['id']})\n";
        continue;
    }
    
    // Perform dynamic import
    $import_result = dynamicProductImport($wp_product, $import_config);
    $all_results[] = $import_result;
    
    if ($import_result['success']) {
        $success_count++;
    } else {
        $error_count++;
    }
    
    // Add a small delay between products to avoid API rate limits
    sleep(1);
}

// Summary Report
echo "\n" . str_repeat("=", 80) . "\n";
echo "IMPORT SUMMARY REPORT\n";
echo str_repeat("=", 80) . "\n";
echo "Total Processed: $processed_count\n";
echo "Successful: $success_count\n";
echo "Failed: $error_count\n";
echo "Success Rate: " . ($processed_count > 0 ? round(($success_count / $processed_count) * 100, 2) : 0) . "%\n";

if (!$import_config['dry_run']) {
    echo "\nDetailed Results:\n";
    echo str_repeat("-", 80) . "\n";
    
    foreach ($all_results as $result) {
        echo "Product: {$result['wp_product_name']} (WP ID: {$result['wp_product_id']})\n";
        echo "Status: " . ($result['success'] ? '✓ SUCCESS' : '✗ FAILED') . "\n";
        
        if ($result['bc_product_id']) {
            echo "BigCommerce Product ID: {$result['bc_product_id']}\n";
        }
        
        if (!empty($result['created_options'])) {
            echo "Created Options: " . count($result['created_options']) . "\n";
        }
        
        if (!empty($result['created_variants'])) {
            echo "Created Variants: " . count($result['created_variants']) . "\n";
        }
        
        if (!empty($result['pricing_sync_results'])) {
            echo "Price List Sync Results:\n";
            foreach ($result['pricing_sync_results'] as $type => $sync_result) {
                $status = $sync_result['success'] ? '✓' : '✗';
                echo "  $status $type: {$sync_result['count']} prices synced\n";
            }
        }
        
        if (!empty($result['errors'])) {
            echo "Errors:\n";
            foreach ($result['errors'] as $error) {
                echo "  - $error\n";
            }
        }
        
        echo str_repeat("-", 40) . "\n";
    }
}

echo "\nNext Steps:\n";
echo "1. Check your BigCommerce admin panel to verify the imported products\n";
echo "2. Verify that price lists have been populated correctly\n";
echo "3. Assign price lists to customer groups if not already done\n";
echo "4. Test the products and pricing on your BigCommerce storefront\n";

echo "\n=== IMPORT COMPLETE ===\n";

/**
 * Helper function to display import progress
 */
function displayImportProgress($current, $total, $product_name) {
    $percentage = round(($current / $total) * 100, 1);
    $bar_length = 50;
    $filled_length = round(($current / $total) * $bar_length);
    
    $bar = str_repeat('█', $filled_length) . str_repeat('░', $bar_length - $filled_length);
    
    echo "\rProgress: [$bar] $percentage% ($current/$total) - $product_name";
    
    if ($current == $total) {
        echo "\n";
    }
}

/**
 * Validate import configuration
 */
function validateImportConfig($config) {
    $errors = [];
    
    if (!isset($config['price_list_config']) || empty($config['price_list_config'])) {
        $errors[] = "Price list configuration is required";
    }
    
    if (!isset($config['batch_size']) || $config['batch_size'] < 1) {
        $errors[] = "Batch size must be at least 1";
    }
    
    foreach ($config['price_list_config'] as $type => $price_config) {
        if (!isset($price_config['bc_price_list_id'])) {
            $errors[] = "BigCommerce price list ID missing for $type";
        }
        if (!isset($price_config['wp_role_id'])) {
            $errors[] = "WordPress role ID missing for $type";
        }
    }
    
    return $errors;
}

// Validate configuration
$config_errors = validateImportConfig($import_config);
if (!empty($config_errors)) {
    echo "Configuration Errors:\n";
    foreach ($config_errors as $error) {
        echo "- $error\n";
    }
    exit(1);
}

?>
