<?php
/**
 * WordPress to BigCommerce Price List Sync
 * 
 * This script syncs wholesale pricing from WordPress/WooCommerce 
 * to BigCommerce price lists.
 */

include("config/config.php");

echo "=== WordPress to BigCommerce Price Sync ===\n\n";

// Configuration
$wordpress_product_id = 104733; // Bad Ace Soap product
$sync_config = [
    'wholesale' => [
        'wp_role_id' => 453,
        'bc_price_list_name' => 'Wholesale',
        'bc_price_list_id' => null // Will be created or found
    ],
    'canada_wholesale' => [
        'wp_role_id' => 455,
        'bc_price_list_name' => 'Canada Wholesale',
        'bc_price_list_id' => null // Will be created or found
    ]
];

// Step 1: Get WordPress product variants with pricing
echo "1. Getting WordPress product variants with wholesale pricing...\n";
$wp_variants = getProductVariantsWithPricing($wordpress_product_id);

if (!$wp_variants) {
    echo "✗ Failed to get WordPress variants\n";
    exit(1);
}

echo "✓ Found " . count($wp_variants) . " WordPress variants\n";

$variants_to_sync = [];
foreach ($wp_variants as $variant) {
    if (!empty($variant['wholesale_pricing'])) {
        $variants_to_sync[] = $variant;
        echo "- Variant " . $variant['id'] . " (" . $variant['sku'] . ") has wholesale pricing\n";
    }
}

if (empty($variants_to_sync)) {
    echo "No variants with wholesale pricing found\n";
    exit(0);
}

echo "\n" . str_repeat("-", 60) . "\n\n";

// Step 2: Find or create BigCommerce price lists
echo "2. Setting up BigCommerce price lists...\n";

$existing_bc_lists = getBigCommercePriceLists();
$existing_lists_by_name = [];

if ($existing_bc_lists) {
    foreach ($existing_bc_lists as $list) {
        $existing_lists_by_name[$list['name']] = $list['id'];
    }
}

foreach ($sync_config as $type => &$config) {
    $list_name = $config['bc_price_list_name'];
    
    if (isset($existing_lists_by_name[$list_name])) {
        $config['bc_price_list_id'] = $existing_lists_by_name[$list_name];
        echo "✓ Found existing BigCommerce price list '$list_name' (ID: " . $config['bc_price_list_id'] . ")\n";
    } else {
        // Create new price list
        $new_list_data = [
            'name' => $list_name,
            'active' => true
        ];
        
        $new_list_id = createBigCommercePriceList($new_list_data);
        if ($new_list_id) {
            $config['bc_price_list_id'] = $new_list_id;
            echo "✓ Created new BigCommerce price list '$list_name' (ID: $new_list_id)\n";
        } else {
            echo "✗ Failed to create BigCommerce price list '$list_name'\n";
        }
    }
}

echo "\n" . str_repeat("-", 60) . "\n\n";

// Step 3: Map WordPress variants to BigCommerce variants
echo "3. Mapping WordPress variants to BigCommerce variants...\n";

$variant_mapping = [];
foreach ($variants_to_sync as $wp_variant) {
    $sku = $wp_variant['sku'];
    $bc_variant_id = getBigCommerceVariantBySku($sku);
    
    if ($bc_variant_id) {
        $variant_mapping[$wp_variant['id']] = [
            'wp_variant' => $wp_variant,
            'bc_variant_id' => $bc_variant_id,
            'sku' => $sku
        ];
        echo "✓ Mapped WP variant " . $wp_variant['id'] . " to BC variant $bc_variant_id (SKU: $sku)\n";
    } else {
        echo "✗ No BigCommerce variant found for SKU: $sku\n";
    }
}

if (empty($variant_mapping)) {
    echo "No variant mappings found - cannot proceed with sync\n";
    exit(1);
}

echo "\n" . str_repeat("-", 60) . "\n\n";

// Step 4: Sync pricing data
echo "4. Syncing pricing data to BigCommerce...\n";

$sync_results = [];

foreach ($sync_config as $type => $config) {
    if (!$config['bc_price_list_id']) {
        echo "Skipping $type - no BigCommerce price list available\n";
        continue;
    }
    
    echo "\nSyncing $type pricing (BC Price List ID: " . $config['bc_price_list_id'] . ")...\n";
    
    $price_records = [];
    $sync_count = 0;
    
    foreach ($variant_mapping as $wp_variant_id => $mapping) {
        $wp_variant = $mapping['wp_variant'];
        $bc_variant_id = $mapping['bc_variant_id'];
        
        // Check if this variant has pricing for this type
        $price_key = ($type === 'wholesale') ? 'wholesale' : 'canada_wholesale';
        
        if (isset($wp_variant['wholesale_pricing'][$price_key])) {
            $price = $wp_variant['wholesale_pricing'][$price_key]['price'];
            
            $price_records[$bc_variant_id] = floatval($price);
            $sync_count++;
            
            echo "- WP Variant $wp_variant_id -> BC Variant $bc_variant_id: $$price\n";
        }
    }
    
    if (!empty($price_records)) {
        $batch_result = batchAssignBigCommercePrices($config['bc_price_list_id'], $price_records);
        
        if ($batch_result) {
            echo "✓ Successfully synced $sync_count prices for $type\n";
            $sync_results[$type] = [
                'success' => true,
                'count' => $sync_count,
                'price_list_id' => $config['bc_price_list_id']
            ];
        } else {
            echo "✗ Failed to sync prices for $type\n";
            $sync_results[$type] = [
                'success' => false,
                'count' => 0,
                'error' => 'Batch assignment failed'
            ];
        }
    } else {
        echo "No pricing data found for $type\n";
        $sync_results[$type] = [
            'success' => true,
            'count' => 0,
            'message' => 'No data to sync'
        ];
    }
}

echo "\n" . str_repeat("-", 60) . "\n\n";

// Step 5: Verification
echo "5. Verifying synced data...\n";

foreach ($sync_config as $type => $config) {
    if (!$config['bc_price_list_id']) continue;
    
    echo "\nVerifying $type price list (ID: " . $config['bc_price_list_id'] . ")...\n";
    
    $price_records = getBigCommercePriceRecords($config['bc_price_list_id']);
    
    if ($price_records) {
        echo "✓ Found " . count($price_records) . " price records\n";
        foreach ($price_records as $record) {
            echo "- Variant " . $record['variant_id'] . ": $" . $record['price'] . "\n";
        }
    } else {
        echo "✗ No price records found or error retrieving data\n";
    }
}

echo "\n" . str_repeat("-", 60) . "\n\n";

// Summary
echo "=== Sync Summary ===\n";
echo "WordPress Product ID: $wordpress_product_id\n";
echo "Variants processed: " . count($variant_mapping) . "\n\n";

foreach ($sync_results as $type => $result) {
    $status = $result['success'] ? '✓' : '✗';
    echo "$status $type: ";
    
    if ($result['success']) {
        echo $result['count'] . " prices synced";
        if (isset($result['price_list_id'])) {
            echo " (BC Price List ID: " . $result['price_list_id'] . ")";
        }
        if (isset($result['message'])) {
            echo " - " . $result['message'];
        }
    } else {
        echo "Failed";
        if (isset($result['error'])) {
            echo " - " . $result['error'];
        }
    }
    echo "\n";
}

echo "\nNext Steps:\n";
echo "1. Check your BigCommerce admin panel to verify the price lists\n";
echo "2. Assign the price lists to customer groups in BigCommerce\n";
echo "3. Test the pricing on your BigCommerce storefront\n";
echo "4. Set up automated sync if needed\n";

echo "\n=== Sync Complete ===\n";

?>
