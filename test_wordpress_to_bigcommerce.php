<?php
/**
 * Simple test script for WordPress to BigCommerce import functionality
 * 
 * This script tests the core functions needed to get WordPress data
 * and import it into BigCommerce price lists.
 */

include("config/config.php");

echo "=== WordPress to BigCommerce Test ===\n\n";

// Test 1: Get WordPress product variants
echo "1. Testing WordPress data retrieval...\n";
$product_id = 104733; // Bad Ace Soap product
$variants = getProductVariantsWithPricing($product_id);

if ($variants) {
    echo "✓ Successfully retrieved " . count($variants) . " WordPress variants\n";
    
    $variants_with_pricing = 0;
    foreach ($variants as $variant) {
        if (!empty($variant['wholesale_pricing'])) {
            $variants_with_pricing++;
            echo "- Variant " . $variant['id'] . " (" . $variant['sku'] . ") has wholesale pricing\n";
        }
    }
    
    echo "- Found $variants_with_pricing variants with wholesale pricing\n";
} else {
    echo "✗ Failed to retrieve WordPress variants\n";
    exit(1);
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 2: Test BigCommerce API connectivity
echo "2. Testing BigCommerce API connectivity...\n";
$existing_lists = getBigCommercePriceLists();

if ($existing_lists !== false) {
    echo "✓ BigCommerce API connection successful\n";
    echo "- Found " . count($existing_lists) . " existing price lists\n";
} else {
    echo "✗ BigCommerce API connection failed\n";
    exit(1);
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 3: Test variant lookup by SKU
echo "3. Testing BigCommerce variant lookup...\n";
$test_sku = '67003.08'; // From WordPress data
$bc_variant_id = getBigCommerceVariantBySku($test_sku);

if ($bc_variant_id) {
    echo "✓ Found BigCommerce variant for SKU $test_sku: ID $bc_variant_id\n";
} else {
    echo "? No BigCommerce variant found for SKU $test_sku\n";
    echo "  (This is normal if the product doesn't exist in BigCommerce yet)\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 4: Test price list creation
echo "4. Testing price list creation...\n";
$test_price_list_data = [
    'name' => 'Test Import List - ' . date('Y-m-d H:i:s'),
    'active' => true
];

$created_list_id = createBigCommercePriceList($test_price_list_data);

if ($created_list_id) {
    echo "✓ Successfully created test price list (ID: $created_list_id)\n";
    
    // Test 5: Test price assignment (if we have a variant)
    if ($bc_variant_id) {
        echo "\n5. Testing price assignment...\n";
        $test_price = 15.99;
        $price_result = assignBigCommercePriceToVariant($created_list_id, $bc_variant_id, $test_price);
        
        if ($price_result) {
            echo "✓ Successfully assigned price $$test_price to variant $bc_variant_id\n";
        } else {
            echo "✗ Failed to assign price\n";
        }
    } else {
        echo "\n5. Skipping price assignment test - no BigCommerce variant found\n";
    }
    
    // Clean up - delete the test price list
    echo "\nCleaning up test price list...\n";
    $delete_result = deleteBigCommercePriceList($created_list_id);
    
    if ($delete_result) {
        echo "✓ Test price list deleted successfully\n";
    } else {
        echo "? Test price list deletion may have failed (ID: $created_list_id)\n";
    }
    
} else {
    echo "✗ Failed to create test price list\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Summary
echo "=== Test Summary ===\n";
echo "WordPress API: " . ($variants ? "✓ Working" : "✗ Failed") . "\n";
echo "BigCommerce API: " . ($existing_lists !== false ? "✓ Working" : "✗ Failed") . "\n";
echo "Variant Lookup: " . ($bc_variant_id ? "✓ Working" : "? No test data") . "\n";
echo "Price List Creation: " . ($created_list_id ? "✓ Working" : "✗ Failed") . "\n";

if ($variants && $existing_lists !== false) {
    echo "\n✓ All core functions are working!\n";
    echo "You can now run the import examples:\n";
    echo "- php examples/wordpress_to_bigcommerce_import.php\n";
    echo "- php sync_wordpress_to_bigcommerce_pricing.php\n";
} else {
    echo "\n✗ Some functions failed - check your API credentials and connectivity\n";
}

echo "\n=== Test Complete ===\n";

?>
