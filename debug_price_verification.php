<?php
/**
 * Debug script to verify price data is being saved correctly
 */

include("config/config.php");

echo "=== Price Verification Debug ===\n\n";

$product_id = 104733;
$variant_id = 104738;

// Get the raw variation data
echo "1. Getting raw variation data from WordPress API...\n";
$url = WP_URL . "products/$product_id/variations/$variant_id?consumer_key=" . COMSUMER_KEY . "&consumer_secret=" . COMSUMER_SECRET;
echo "API URL: $url\n\n";

$response = wp_curl($url);

if ($response) {
    $data = json_decode($response, true);
    
    echo "✓ API Response received\n";
    echo "- Variation ID: " . $data['id'] . "\n";
    echo "- SKU: " . $data['sku'] . "\n";
    echo "- Regular Price: $" . $data['regular_price'] . "\n";
    echo "- Date Modified: " . $data['date_modified'] . "\n\n";
    
    echo "2. Checking meta_data for wholesale pricing...\n";
    
    $wholesale_meta_found = false;
    foreach ($data['meta_data'] as $meta) {
        if ($meta['key'] === 'wholesale_multi_user_pricing') {
            $wholesale_meta_found = true;
            echo "✓ Found wholesale_multi_user_pricing meta data\n";
            echo "Raw wholesale pricing data:\n";
            print_r($meta['value']);
            
            // Check specific price list values
            $wholesale_pricing = $meta['value'];
            
            if (isset($wholesale_pricing[453])) {
                echo "\nWholesale (Role ID 453):\n";
                if (isset($wholesale_pricing[453][$variant_id])) {
                    echo "- Price: $" . $wholesale_pricing[453][$variant_id]['wholesaleprice'] . "\n";
                    echo "- Qty: " . $wholesale_pricing[453][$variant_id]['qty'] . "\n";
                    echo "- Step: " . $wholesale_pricing[453][$variant_id]['step'] . "\n";
                } else {
                    echo "- No pricing data for variant $variant_id\n";
                }
            } else {
                echo "\nNo Wholesale (Role ID 453) data found\n";
            }
            
            if (isset($wholesale_pricing[455])) {
                echo "\nCanada Wholesale (Role ID 455):\n";
                if (isset($wholesale_pricing[455][$variant_id])) {
                    echo "- Price: $" . $wholesale_pricing[455][$variant_id]['wholesaleprice'] . "\n";
                    echo "- Qty: " . $wholesale_pricing[455][$variant_id]['qty'] . "\n";
                    echo "- Step: " . $wholesale_pricing[455][$variant_id]['step'] . "\n";
                } else {
                    echo "- No pricing data for variant $variant_id\n";
                }
            } else {
                echo "\nNo Canada Wholesale (Role ID 455) data found\n";
            }
            
            break;
        }
    }
    
    if (!$wholesale_meta_found) {
        echo "✗ No wholesale_multi_user_pricing meta data found\n";
    }
    
    echo "\n3. Checking all meta_data keys...\n";
    foreach ($data['meta_data'] as $meta) {
        echo "- " . $meta['key'] . "\n";
    }
    
} else {
    echo "✗ Failed to get API response\n";
}

echo "\n4. Testing direct price assignment to verify API connectivity...\n";

// Test a simple price assignment
$test_price = 9.99;
$result = assignVariantPriceList($product_id, $variant_id, 2, $test_price);

if ($result) {
    echo "✓ Price assignment function returned success\n";
    echo "Response data keys: " . implode(', ', array_keys($result)) . "\n";
    
    if (isset($result['date_modified'])) {
        echo "- Last modified: " . $result['date_modified'] . "\n";
    }
} else {
    echo "✗ Price assignment function failed\n";
}

echo "\n5. Re-checking data after assignment...\n";
$updated_response = wp_curl($url);

if ($updated_response) {
    $updated_data = json_decode($updated_response, true);
    echo "- Date Modified: " . $updated_data['date_modified'] . "\n";
    
    foreach ($updated_data['meta_data'] as $meta) {
        if ($meta['key'] === 'wholesale_multi_user_pricing') {
            $pricing = $meta['value'];
            if (isset($pricing[453][$variant_id])) {
                echo "- Current Wholesale Price: $" . $pricing[453][$variant_id]['wholesaleprice'] . "\n";
            }
            break;
        }
    }
} else {
    echo "✗ Failed to get updated response\n";
}

echo "\n=== Debug Complete ===\n";

?>
