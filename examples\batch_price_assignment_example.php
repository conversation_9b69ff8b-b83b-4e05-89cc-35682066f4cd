<?php
/**
 * Example usage of batch price list assignment functions
 * 
 * This example demonstrates how to use the batch functions for efficient
 * bulk price assignments and product analysis.
 */

include("../config/config.php");

echo "=== Batch Price List Assignment Example ===\n\n";

// Example 1: Get all variants for a product with current pricing
$product_id = 104733;  // Bad Ace Soap product

echo "1. Getting all variants for product $product_id with current pricing...\n";
$variants_with_pricing = getProductVariantsWithPricing($product_id);

if ($variants_with_pricing) {
    echo "✓ Found " . count($variants_with_pricing) . " variants:\n\n";
    
    foreach ($variants_with_pricing as $variant) {
        echo "Variant ID: " . $variant['id'] . "\n";
        echo "- SKU: " . $variant['sku'] . "\n";
        echo "- Name: " . $variant['name'] . "\n";
        echo "- Regular Price: $" . $variant['regular_price'] . "\n";
        
        if (!empty($variant['wholesale_pricing'])) {
            echo "- Wholesale Pricing:\n";
            
            if (isset($variant['wholesale_pricing']['wholesale'])) {
                echo "  * Wholesale: $" . $variant['wholesale_pricing']['wholesale']['price'] . "\n";
            }
            
            if (isset($variant['wholesale_pricing']['canada_wholesale'])) {
                echo "  * Canada Wholesale: $" . $variant['wholesale_pricing']['canada_wholesale']['price'] . "\n";
            }
        } else {
            echo "- No wholesale pricing set\n";
        }
        echo "\n";
    }
} else {
    echo "✗ Failed to get variants with pricing\n";
}

echo str_repeat("-", 60) . "\n\n";

// Example 2: Batch price assignment
echo "2. Performing batch price assignment...\n";

$batch_assignments = [
    [
        'product_id' => 104733,
        'variant_id' => 104738,  // Unscented
        'price_list_id' => 2,    // Wholesale
        'price' => 10.99
    ],
    [
        'product_id' => 104733,
        'variant_id' => 104738,  // Unscented
        'price_list_id' => 3,    // Canada Wholesale
        'price' => 11.99
    ],
    [
        'product_id' => 104733,
        'variant_id' => 104737,  // Lemon Verbena
        'price_list_id' => 2,    // Wholesale
        'price' => 10.99
    ],
    [
        'product_id' => 104733,
        'variant_id' => 104737,  // Lemon Verbena
        'price_list_id' => 3,    // Canada Wholesale
        'price' => 11.99
    ]
];

$batch_result = batchAssignVariantPriceLists($batch_assignments);

echo "Batch assignment results:\n";
echo "- Total assignments: " . $batch_result['summary']['total'] . "\n";
echo "- Successful: " . $batch_result['summary']['successful'] . "\n";
echo "- Failed: " . $batch_result['summary']['failed'] . "\n";
echo "- Success rate: " . $batch_result['summary']['success_rate'] . "%\n\n";

echo "Detailed results:\n";
foreach ($batch_result['results'] as $index => $result) {
    $assignment = $result['assignment'];
    $price_list_name = ($assignment['price_list_id'] == 2) ? 'Wholesale' : 'Canada Wholesale';
    
    if ($result['success']) {
        echo "✓ Assignment #$index: $price_list_name price $" . $assignment['price'] . 
             " assigned to variant " . $assignment['variant_id'] . "\n";
    } else {
        echo "✗ Assignment #$index: Failed to assign $price_list_name price to variant " . 
             $assignment['variant_id'] . " - " . $result['error'] . "\n";
    }
}

echo "\n" . str_repeat("-", 60) . "\n\n";

// Example 3: Verify the batch changes
echo "3. Verifying batch changes...\n";
$updated_variants = getProductVariantsWithPricing($product_id);

if ($updated_variants) {
    echo "Updated pricing after batch assignment:\n\n";
    
    foreach ($updated_variants as $variant) {
        echo "Variant " . $variant['id'] . " (" . $variant['name'] . "):\n";
        
        if (!empty($variant['wholesale_pricing'])) {
            if (isset($variant['wholesale_pricing']['wholesale'])) {
                echo "- Wholesale: $" . $variant['wholesale_pricing']['wholesale']['price'] . "\n";
            }
            
            if (isset($variant['wholesale_pricing']['canada_wholesale'])) {
                echo "- Canada Wholesale: $" . $variant['wholesale_pricing']['canada_wholesale']['price'] . "\n";
            }
        } else {
            echo "- No wholesale pricing\n";
        }
        echo "\n";
    }
} else {
    echo "✗ Failed to get updated variants\n";
}

echo str_repeat("-", 60) . "\n\n";

// Example 4: Error handling demonstration
echo "4. Demonstrating error handling...\n";

$invalid_assignments = [
    [
        // Missing variant_id
        'product_id' => 104733,
        'price_list_id' => 2,
        'price' => 15.00
    ],
    [
        // Invalid price list ID
        'product_id' => 104733,
        'variant_id' => 104738,
        'price_list_id' => 999,
        'price' => 15.00
    ],
    [
        // Valid assignment for comparison
        'product_id' => 104733,
        'variant_id' => 104738,
        'price_list_id' => 2,
        'price' => 15.00
    ]
];

$error_test_result = batchAssignVariantPriceLists($invalid_assignments);

echo "Error handling test results:\n";
foreach ($error_test_result['results'] as $index => $result) {
    if ($result['success']) {
        echo "✓ Assignment #$index: Successful\n";
    } else {
        echo "✗ Assignment #$index: " . $result['error'] . "\n";
    }
}

echo "\nError handling summary:\n";
echo "- Total: " . $error_test_result['summary']['total'] . "\n";
echo "- Successful: " . $error_test_result['summary']['successful'] . "\n";
echo "- Failed: " . $error_test_result['summary']['failed'] . "\n";

echo "\n=== Batch Example Complete ===\n";

?>
