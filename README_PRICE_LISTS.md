# Price List Assignment Functions

This document describes the new price list assignment functionality added to the WordPress/WooCommerce integration.

## Overview

The price list assignment functions allow you to programmatically assign wholesale pricing to product variants using the WordPress REST API. This integrates with the existing wholesale pricing system in WooCommerce.

## Available Price Lists

- **Wholesale** (ID: 2) - Standard wholesale pricing
- **Canada Wholesale** (ID: 3) - Canadian wholesale pricing

## Functions

### `assignVariantPriceList($product_id, $variant_id, $price_list_id, $price)`

Assigns a specific price to a product variant for a given price list.

**Parameters:**
- `$product_id` (int) - WordPress product ID
- `$variant_id` (int) - WordPress variation ID
- `$price_list_id` (int) - Price list ID (2 for Wholesale, 3 for Canada Wholesale)
- `$price` (float) - New wholesale price

**Returns:**
- `array` - Updated variation data on success
- `false` - On failure

**Example:**
```php
// Assign wholesale price of $12.00 to variant 104738
$result = assignVariantPriceList(104733, 104738, 2, 12.00);

if ($result) {
    echo "Price assigned successfully!";
} else {
    echo "Failed to assign price.";
}
```

### `getWooProductVariation($product_id, $variant_id)`

Retrieves a specific WordPress product variation.

**Parameters:**
- `$product_id` (int) - WordPress product ID
- `$variant_id` (int) - WordPress variation ID

**Returns:**
- `array` - Variation data on success
- `false` - On failure

### `updateWooProductVariation($product_id, $variant_id, $data)`

Updates a WordPress product variation with new data.

**Parameters:**
- `$product_id` (int) - WordPress product ID
- `$variant_id` (int) - WordPress variation ID
- `$data` (array) - Update data

**Returns:**
- `array` - Updated variation data on success
- `false` - On failure

### `wp_curl_put($url, $data)`

WordPress cURL function for PUT requests.

**Parameters:**
- `$url` (string) - The URL to send the request to
- `$data` (array) - The data to send

**Returns:**
- `string` - Response on success
- `false` - On failure

## Usage Examples

### Single Price Assignment

```php
include("config/config.php");

// Assign Wholesale pricing
$result = assignVariantPriceList(104733, 104738, 2, 12.00);

// Assign Canada Wholesale pricing
$result = assignVariantPriceList(104733, 104738, 3, 13.50);
```

### Bulk Price Assignment

```php
$assignments = [
    ['product_id' => 104733, 'variant_id' => 104738, 'price_list_id' => 2, 'price' => 11.99],
    ['product_id' => 104733, 'variant_id' => 104738, 'price_list_id' => 3, 'price' => 12.99],
    ['product_id' => 104733, 'variant_id' => 104737, 'price_list_id' => 2, 'price' => 11.99],
    ['product_id' => 104733, 'variant_id' => 104737, 'price_list_id' => 3, 'price' => 12.99]
];

foreach ($assignments as $assignment) {
    $result = assignVariantPriceList(
        $assignment['product_id'],
        $assignment['variant_id'],
        $assignment['price_list_id'],
        $assignment['price']
    );
    
    if ($result) {
        echo "✓ Price assigned successfully\n";
    } else {
        echo "✗ Failed to assign price\n";
    }
}
```

### Checking Current Pricing

```php
$variation = getWooProductVariation(104733, 104738);

if ($variation) {
    foreach ($variation['meta_data'] as $meta) {
        if ($meta['key'] === 'wholesale_multi_user_pricing') {
            $pricing = $meta['value'];
            
            // Check Wholesale pricing (role ID 453)
            if (isset($pricing[453])) {
                echo "Wholesale: $" . $pricing[453][104738]['wholesaleprice'] . "\n";
            }
            
            // Check Canada Wholesale pricing (role ID 455)
            if (isset($pricing[455])) {
                echo "Canada Wholesale: $" . $pricing[455][104738]['wholesaleprice'] . "\n";
            }
            break;
        }
    }
}
```

## Data Structure

The wholesale pricing is stored in the variation's meta data under the key `wholesale_multi_user_pricing` with this structure:

```php
[
    453 => [  // Wholesale role ID
        'slug' => 'wholesale',
        'discount_type' => 'fixed',
        104738 => [  // Variant ID
            'wholesaleprice' => '12.00',
            'qty' => '1',
            'step' => ''
        ]
    ],
    455 => [  // Canada Wholesale role ID
        'slug' => 'canada-wholesale',
        'discount_type' => 'fixed',
        104738 => [  // Variant ID
            'wholesaleprice' => '13.50',
            'qty' => '1',
            'step' => ''
        ]
    ]
]
```

## Testing

Run the test script to verify functionality:

```bash
php test_price_list_assignment.php
```

See the example file for more detailed usage:

```bash
php examples/assign_price_list_example.php
```

## Error Handling

The functions include comprehensive error handling:

- Invalid price list IDs are rejected
- Failed API calls return `false`
- Errors are logged using `error_log()`
- Input validation ensures data integrity

## Integration

These functions integrate seamlessly with the existing codebase:

- Uses the same WordPress API credentials from `config/config.php`
- Follows the same naming conventions as existing functions
- Uses the established `wp_curl()` pattern for API calls
- Maintains the existing wholesale pricing data structure
