# WordPress to BigCommerce Price List Import

This document describes the functionality for importing wholesale pricing data from WordPress/WooCommerce into BigCommerce price lists.

## Overview

The import system allows you to:
1. **Read**: Get wholesale pricing data from WordPress/WooCommerce products and variants
2. **Create**: Create and manage price lists in BigCommerce using the API
3. **Import**: Transfer pricing data from WordPress to BigCommerce price lists
4. **Sync**: Keep pricing synchronized between both systems

## Available Price Lists

### WordPress/WooCommerce (Source Data)
- **Wholesale** (Role ID: 453) - Standard wholesale pricing
- **Canada Wholesale** (Role ID: 455) - Canadian wholesale pricing

### BigCommerce (Target System)
- **Wholesale** - Standard wholesale pricing (use existing price list ID)
- **Canada Wholesale** - Canadian wholesale pricing (use existing price list ID)

## Configuration

Before using the import functions, you need to configure your existing BigCommerce price list IDs in `config/price_list_config.php`:

```php
// Update these with your actual BigCommerce price list IDs
define('BC_WHOLESALE_PRICE_LIST_ID', 2);        // Your Wholesale price list ID
define('BC_CANADA_WHOLESALE_PRICE_LIST_ID', 3); // Your Canada Wholesale price list ID
```

**Note:** Since you've already created the price lists and assigned them to customer groups in BigCommerce, you just need to update the configuration file with your actual price list IDs.

## Functions

## WordPress Data Retrieval Functions

### `getWooProductVariation($product_id, $variant_id)`

Retrieves a specific WordPress product variation.

**Parameters:**
- `$product_id` (int) - WordPress product ID
- `$variant_id` (int) - WordPress variation ID

**Returns:**
- `array` - Variation data on success
- `false` - On failure

### `getProductVariantsWithPricing($product_id)`

Gets all variants for a WordPress product with their current wholesale pricing.

**Parameters:**
- `$product_id` (int) - WordPress product ID

**Returns:**
- `array` - Array of variants with pricing data on success
- `false` - On failure

## BigCommerce Functions

### `getBigCommercePriceLists()`

Retrieves all BigCommerce price lists.

**Returns:**
- `array` - Array of price lists on success
- `false` - On failure

### `createBigCommercePriceList($data)`

Creates a new BigCommerce price list.

**Parameters:**
- `$data` (array) - Price list data (name, active status, etc.)

**Returns:**
- `int` - Price list ID on success
- `false` - On failure

### `assignBigCommercePriceToVariant($price_list_id, $variant_id, $price, $currency_code = 'USD')`

Assigns a specific price to a BigCommerce variant in a price list.

**Parameters:**
- `$price_list_id` (int) - BigCommerce price list ID
- `$variant_id` (int) - BigCommerce variant ID
- `$price` (float) - Price to assign
- `$currency_code` (string) - Currency code (default: USD)

**Returns:**
- `bool` - True on success, false on failure

### `batchAssignBigCommercePrices($price_list_id, $variant_prices, $currency_code = 'USD')`

Batch assigns prices to multiple BigCommerce variants.

**Parameters:**
- `$price_list_id` (int) - BigCommerce price list ID
- `$variant_prices` (array) - Array of variant_id => price pairs
- `$currency_code` (string) - Currency code (default: USD)

**Returns:**
- `array` - Result summary on success
- `false` - On failure

### `getBigCommerceVariantBySku($sku)`

Finds a BigCommerce variant ID by SKU.

**Parameters:**
- `$sku` (string) - Product/variant SKU

**Returns:**
- `int` - Variant ID on success
- `false` - On failure



## Usage Examples

### WordPress Data Retrieval Examples

### Get Product Variants with Pricing

```php
include("config/config.php");

// Get all variants for a product with wholesale pricing
$variants = getProductVariantsWithPricing(104733);

foreach ($variants as $variant) {
    echo "Variant: " . $variant['name'] . " (SKU: " . $variant['sku'] . ")\n";

    if (!empty($variant['wholesale_pricing'])) {
        if (isset($variant['wholesale_pricing']['wholesale'])) {
            echo "- Wholesale: $" . $variant['wholesale_pricing']['wholesale']['price'] . "\n";
        }

        if (isset($variant['wholesale_pricing']['canada_wholesale'])) {
            echo "- Canada Wholesale: $" . $variant['wholesale_pricing']['canada_wholesale']['price'] . "\n";
        }
    }
}
```

### Get Single Variation Data

```php
$variation = getWooProductVariation(104733, 104738);

if ($variation) {
    echo "SKU: " . $variation['sku'] . "\n";
    echo "Regular Price: $" . $variation['regular_price'] . "\n";

    // Extract wholesale pricing from meta data
    foreach ($variation['meta_data'] as $meta) {
        if ($meta['key'] === 'wholesale_multi_user_pricing') {
            $pricing = $meta['value'];

            // Check Wholesale pricing (role ID 453)
            if (isset($pricing[453])) {
                echo "Wholesale: $" . $pricing[453][104738]['wholesaleprice'] . "\n";
            }

            // Check Canada Wholesale pricing (role ID 455)
            if (isset($pricing[455])) {
                echo "Canada Wholesale: $" . $pricing[455][104738]['wholesaleprice'] . "\n";
            }
            break;
        }
    }
}
```

### BigCommerce Examples

#### Use Existing Price Lists

```php
include("config/config.php");
include("config/price_list_config.php");

// Get your existing price list IDs
$wholesale_id = getBigCommercePriceListId('wholesale');
$canada_wholesale_id = getBigCommercePriceListId('canada_wholesale');

echo "Wholesale Price List ID: $wholesale_id\n";
echo "Canada Wholesale Price List ID: $canada_wholesale_id\n";
```

#### Assign Pricing to BigCommerce Variants

```php
// Single price assignment
$price_list_id = 1; // BigCommerce price list ID
$variant_id = 123;  // BigCommerce variant ID
$price = 15.99;

$result = assignBigCommercePriceToVariant($price_list_id, $variant_id, $price);

// Batch price assignment
$variant_prices = [
    123 => 15.99,
    124 => 16.99,
    125 => 17.99
];

$batch_result = batchAssignBigCommercePrices($price_list_id, $variant_prices);
```

#### Find BigCommerce Variants by SKU

```php
$sku = '67003.08';
$bc_variant_id = getBigCommerceVariantBySku($sku);

if ($bc_variant_id) {
    echo "Found BigCommerce variant ID: $bc_variant_id\n";
}
```

#### Complete WordPress to BigCommerce Import

```php
// Run the complete import example
php examples/wordpress_to_bigcommerce_import.php

// Or run the sync script
php sync_wordpress_to_bigcommerce_pricing.php
```

## Data Structure

The wholesale pricing is stored in the variation's meta data under the key `wholesale_multi_user_pricing` with this structure:

```php
[
    453 => [  // Wholesale role ID
        'slug' => 'wholesale',
        'discount_type' => 'fixed',
        104738 => [  // Variant ID
            'wholesaleprice' => '12.00',
            'qty' => '1',
            'step' => ''
        ]
    ],
    455 => [  // Canada Wholesale role ID
        'slug' => 'canada-wholesale',
        'discount_type' => 'fixed',
        104738 => [  // Variant ID
            'wholesaleprice' => '13.50',
            'qty' => '1',
            'step' => ''
        ]
    ]
]
```

## Testing

### WordPress Data Retrieval Testing

Test getting WordPress product data:

```bash
php examples/wordpress_to_bigcommerce_import.php
```

### BigCommerce Testing

Run the BigCommerce test script:

```bash
php test_bigcommerce_price_lists.php
```

See the BigCommerce example file:

```bash
php examples/bigcommerce_price_lists_example.php
```

### Complete Integration Testing

Run the sync script to test the complete WordPress to BigCommerce integration:

```bash
php sync_wordpress_to_bigcommerce_pricing.php
```

## Error Handling

The functions include comprehensive error handling:

- Invalid price list IDs are rejected
- Failed API calls return `false`
- Errors are logged using `error_log()`
- Input validation ensures data integrity

## Integration

These functions integrate seamlessly with the existing codebase:

- Uses the same WordPress API credentials from `config/config.php`
- Follows the same naming conventions as existing functions
- Uses the established `wp_curl()` pattern for API calls
- Maintains the existing wholesale pricing data structure
