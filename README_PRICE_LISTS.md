# Price List Assignment Functions

This document describes the comprehensive price list assignment functionality for both WordPress/WooCommerce and BigCommerce integrations.

## Overview

The price list assignment functions allow you to:
1. **WordPress/WooCommerce**: Assign wholesale pricing to product variants using the WordPress REST API
2. **BigCommerce**: Create and manage price lists and assign variant-level pricing using the BigCommerce API
3. **Sync**: Synchronize pricing data between WordPress and BigCommerce systems

## Available Price Lists

### WordPress/WooCommerce
- **Wholesale** (ID: 2) - Standard wholesale pricing
- **Canada Wholesale** (ID: 3) - Canadian wholesale pricing

### BigCommerce
- **Wholesale** - Standard wholesale pricing (created dynamically)
- **Canada Wholesale** - Canadian wholesale pricing (created dynamically)

## Functions

## WordPress/WooCommerce Functions

### `assignVariantPriceList($product_id, $variant_id, $price_list_id, $price)`

Assigns a specific price to a product variant for a given price list.

**Parameters:**
- `$product_id` (int) - WordPress product ID
- `$variant_id` (int) - WordPress variation ID
- `$price_list_id` (int) - Price list ID (2 for Wholesale, 3 for Canada Wholesale)
- `$price` (float) - New wholesale price

**Returns:**
- `array` - Updated variation data on success
- `false` - On failure

**Example:**
```php
// Assign wholesale price of $12.00 to variant 104738
$result = assignVariantPriceList(104733, 104738, 2, 12.00);

if ($result) {
    echo "Price assigned successfully!";
} else {
    echo "Failed to assign price.";
}
```

### `getWooProductVariation($product_id, $variant_id)`

Retrieves a specific WordPress product variation.

**Parameters:**
- `$product_id` (int) - WordPress product ID
- `$variant_id` (int) - WordPress variation ID

**Returns:**
- `array` - Variation data on success
- `false` - On failure

### `updateWooProductVariation($product_id, $variant_id, $data)`

Updates a WordPress product variation with new data.

**Parameters:**
- `$product_id` (int) - WordPress product ID
- `$variant_id` (int) - WordPress variation ID
- `$data` (array) - Update data

**Returns:**
- `array` - Updated variation data on success
- `false` - On failure

### `wp_curl_put($url, $data)`

WordPress cURL function for PUT requests.

**Parameters:**
- `$url` (string) - The URL to send the request to
- `$data` (array) - The data to send

**Returns:**
- `string` - Response on success
- `false` - On failure

## BigCommerce Functions

### `getBigCommercePriceLists()`

Retrieves all BigCommerce price lists.

**Returns:**
- `array` - Array of price lists on success
- `false` - On failure

### `createBigCommercePriceList($data)`

Creates a new BigCommerce price list.

**Parameters:**
- `$data` (array) - Price list data (name, active status, etc.)

**Returns:**
- `int` - Price list ID on success
- `false` - On failure

### `assignBigCommercePriceToVariant($price_list_id, $variant_id, $price, $currency_code = 'USD')`

Assigns a specific price to a BigCommerce variant in a price list.

**Parameters:**
- `$price_list_id` (int) - BigCommerce price list ID
- `$variant_id` (int) - BigCommerce variant ID
- `$price` (float) - Price to assign
- `$currency_code` (string) - Currency code (default: USD)

**Returns:**
- `bool` - True on success, false on failure

### `batchAssignBigCommercePrices($price_list_id, $variant_prices, $currency_code = 'USD')`

Batch assigns prices to multiple BigCommerce variants.

**Parameters:**
- `$price_list_id` (int) - BigCommerce price list ID
- `$variant_prices` (array) - Array of variant_id => price pairs
- `$currency_code` (string) - Currency code (default: USD)

**Returns:**
- `array` - Result summary on success
- `false` - On failure

### `getBigCommerceVariantBySku($sku)`

Finds a BigCommerce variant ID by SKU.

**Parameters:**
- `$sku` (string) - Product/variant SKU

**Returns:**
- `int` - Variant ID on success
- `false` - On failure

### `createWholesalePriceLists()`

Creates both Wholesale and Canada Wholesale price lists in BigCommerce.

**Returns:**
- `array` - Results for both created price lists

## Usage Examples

### WordPress/WooCommerce Examples

### Single Price Assignment

```php
include("config/config.php");

// Assign Wholesale pricing
$result = assignVariantPriceList(104733, 104738, 2, 12.00);

// Assign Canada Wholesale pricing
$result = assignVariantPriceList(104733, 104738, 3, 13.50);
```

### Bulk Price Assignment

```php
$assignments = [
    ['product_id' => 104733, 'variant_id' => 104738, 'price_list_id' => 2, 'price' => 11.99],
    ['product_id' => 104733, 'variant_id' => 104738, 'price_list_id' => 3, 'price' => 12.99],
    ['product_id' => 104733, 'variant_id' => 104737, 'price_list_id' => 2, 'price' => 11.99],
    ['product_id' => 104733, 'variant_id' => 104737, 'price_list_id' => 3, 'price' => 12.99]
];

foreach ($assignments as $assignment) {
    $result = assignVariantPriceList(
        $assignment['product_id'],
        $assignment['variant_id'],
        $assignment['price_list_id'],
        $assignment['price']
    );
    
    if ($result) {
        echo "✓ Price assigned successfully\n";
    } else {
        echo "✗ Failed to assign price\n";
    }
}
```

### Checking Current Pricing

```php
$variation = getWooProductVariation(104733, 104738);

if ($variation) {
    foreach ($variation['meta_data'] as $meta) {
        if ($meta['key'] === 'wholesale_multi_user_pricing') {
            $pricing = $meta['value'];
            
            // Check Wholesale pricing (role ID 453)
            if (isset($pricing[453])) {
                echo "Wholesale: $" . $pricing[453][104738]['wholesaleprice'] . "\n";
            }
            
            // Check Canada Wholesale pricing (role ID 455)
            if (isset($pricing[455])) {
                echo "Canada Wholesale: $" . $pricing[455][104738]['wholesaleprice'] . "\n";
            }
            break;
        }
    }
}
```

### BigCommerce Examples

#### Create Price Lists

```php
include("config/config.php");

// Create wholesale price lists
$created_lists = createWholesalePriceLists();

foreach ($created_lists as $type => $result) {
    if ($result['success']) {
        echo "Created " . $result['name'] . " (ID: " . $result['id'] . ")\n";
    }
}
```

#### Assign Pricing to BigCommerce Variants

```php
// Single price assignment
$price_list_id = 1; // BigCommerce price list ID
$variant_id = 123;  // BigCommerce variant ID
$price = 15.99;

$result = assignBigCommercePriceToVariant($price_list_id, $variant_id, $price);

// Batch price assignment
$variant_prices = [
    123 => 15.99,
    124 => 16.99,
    125 => 17.99
];

$batch_result = batchAssignBigCommercePrices($price_list_id, $variant_prices);
```

#### Find BigCommerce Variants by SKU

```php
$sku = '67003.08';
$bc_variant_id = getBigCommerceVariantBySku($sku);

if ($bc_variant_id) {
    echo "Found BigCommerce variant ID: $bc_variant_id\n";
}
```

#### Sync WordPress to BigCommerce

```php
// Run the complete sync script
php sync_wordpress_to_bigcommerce_pricing.php
```

## Data Structure

The wholesale pricing is stored in the variation's meta data under the key `wholesale_multi_user_pricing` with this structure:

```php
[
    453 => [  // Wholesale role ID
        'slug' => 'wholesale',
        'discount_type' => 'fixed',
        104738 => [  // Variant ID
            'wholesaleprice' => '12.00',
            'qty' => '1',
            'step' => ''
        ]
    ],
    455 => [  // Canada Wholesale role ID
        'slug' => 'canada-wholesale',
        'discount_type' => 'fixed',
        104738 => [  // Variant ID
            'wholesaleprice' => '13.50',
            'qty' => '1',
            'step' => ''
        ]
    ]
]
```

## Testing

### WordPress/WooCommerce Testing

Run the WordPress test script:

```bash
php test_price_list_assignment.php
```

See the WordPress example file:

```bash
php examples/assign_price_list_example.php
```

### BigCommerce Testing

Run the BigCommerce test script:

```bash
php test_bigcommerce_price_lists.php
```

See the BigCommerce example file:

```bash
php examples/bigcommerce_price_lists_example.php
```

### Complete Integration Testing

Run the sync script to test the complete WordPress to BigCommerce integration:

```bash
php sync_wordpress_to_bigcommerce_pricing.php
```

## Error Handling

The functions include comprehensive error handling:

- Invalid price list IDs are rejected
- Failed API calls return `false`
- Errors are logged using `error_log()`
- Input validation ensures data integrity

## Integration

These functions integrate seamlessly with the existing codebase:

- Uses the same WordPress API credentials from `config/config.php`
- Follows the same naming conventions as existing functions
- Uses the established `wp_curl()` pattern for API calls
- Maintains the existing wholesale pricing data structure
