<?php
/**
 * Example usage of the assignVariantPriceList function
 * 
 * This example demonstrates how to assign wholesale pricing to product variants
 * using the WordPress API integration.
 */

include("../config/config.php");

// Example 1: Assign Wholesale pricing (Price List ID: 2)
$product_id = 104733;  // WordPress product ID
$variant_id = 104738;  // WordPress variation ID (Unscented variant)
$price_list_id = 2;    // Wholesale price list
$new_price = 12.00;    // New wholesale price

echo "Assigning Wholesale pricing...\n";
$result = assignVariantPriceList($product_id, $variant_id, $price_list_id, $new_price);

if ($result) {
    echo "✓ Successfully assigned Wholesale price of $$new_price to variant $variant_id\n";
    echo "Updated variation data:\n";
    pre($result);
} else {
    echo "✗ Failed to assign Wholesale pricing\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Example 2: Assign Canada Wholesale pricing (Price List ID: 3)
$product_id = 104733;  // WordPress product ID  
$variant_id = 104737;  // WordPress variation ID (Lemon Verbena variant)
$price_list_id = 3;    // Canada Wholesale price list
$new_price = 13.50;    // New Canada wholesale price

echo "Assigning Canada Wholesale pricing...\n";
$result = assignVariantPriceList($product_id, $variant_id, $price_list_id, $new_price);

if ($result) {
    echo "✓ Successfully assigned Canada Wholesale price of $$new_price to variant $variant_id\n";
    echo "Updated variation data:\n";
    pre($result);
} else {
    echo "✗ Failed to assign Canada Wholesale pricing\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Example 3: Bulk assign pricing to multiple variants
$bulk_assignments = [
    [
        'product_id' => 104733,
        'variant_id' => 104738,
        'price_list_id' => 2,  // Wholesale
        'price' => 11.99
    ],
    [
        'product_id' => 104733,
        'variant_id' => 104738,
        'price_list_id' => 3,  // Canada Wholesale
        'price' => 12.99
    ],
    [
        'product_id' => 104733,
        'variant_id' => 104737,
        'price_list_id' => 2,  // Wholesale
        'price' => 11.99
    ],
    [
        'product_id' => 104733,
        'variant_id' => 104737,
        'price_list_id' => 3,  // Canada Wholesale
        'price' => 12.99
    ]
];

echo "Bulk assigning pricing...\n";
$success_count = 0;
$total_count = count($bulk_assignments);

foreach ($bulk_assignments as $assignment) {
    $result = assignVariantPriceList(
        $assignment['product_id'],
        $assignment['variant_id'],
        $assignment['price_list_id'],
        $assignment['price']
    );
    
    if ($result) {
        $success_count++;
        $price_list_name = ($assignment['price_list_id'] == 2) ? 'Wholesale' : 'Canada Wholesale';
        echo "✓ Assigned $price_list_name price of $" . $assignment['price'] . " to variant " . $assignment['variant_id'] . "\n";
    } else {
        echo "✗ Failed to assign pricing to variant " . $assignment['variant_id'] . "\n";
    }
}

echo "\nBulk assignment complete: $success_count/$total_count successful\n";

// Example 4: Get current variation data to verify pricing
echo "\n" . str_repeat("-", 50) . "\n\n";
echo "Getting current variation data to verify pricing...\n";

$variation_data = getWooProductVariation(104733, 104738);
if ($variation_data) {
    echo "Current pricing for variant 104738:\n";
    
    // Extract wholesale pricing from meta data
    foreach ($variation_data['meta_data'] as $meta) {
        if ($meta['key'] === 'wholesale_multi_user_pricing') {
            $wholesale_pricing = $meta['value'];
            
            if (isset($wholesale_pricing[453])) {  // Wholesale
                echo "- Wholesale (ID: 2): $" . $wholesale_pricing[453][104738]['wholesaleprice'] . "\n";
            }
            
            if (isset($wholesale_pricing[455])) {  // Canada Wholesale
                echo "- Canada Wholesale (ID: 3): $" . $wholesale_pricing[455][104738]['wholesaleprice'] . "\n";
            }
            break;
        }
    }
} else {
    echo "Failed to get variation data\n";
}

?>
