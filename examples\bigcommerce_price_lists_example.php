<?php
/**
 * BigCommerce Price Lists Example
 * 
 * This example demonstrates how to create and manage price lists in BigCommerce
 * and assign pricing to product variants.
 */

include("../config/config.php");

echo "=== BigCommerce Price Lists Example ===\n\n";

// Step 1: Check existing price lists
echo "1. Checking existing BigCommerce price lists...\n";
$existing_price_lists = getBigCommercePriceLists();

if ($existing_price_lists) {
    echo "✓ Found " . count($existing_price_lists) . " existing price lists:\n";
    foreach ($existing_price_lists as $price_list) {
        echo "- ID: " . $price_list['id'] . ", Name: " . $price_list['name'] . ", Active: " . ($price_list['active'] ? 'Yes' : 'No') . "\n";
    }
} else {
    echo "No existing price lists found or API error\n";
}

echo "\n" . str_repeat("-", 60) . "\n\n";

// Step 2: Create wholesale price lists
echo "2. Creating wholesale price lists...\n";
$created_price_lists = createWholesalePriceLists();

echo "\nCreated price lists summary:\n";
foreach ($created_price_lists as $type => $result) {
    if ($result['success']) {
        echo "✓ " . $result['name'] . " - ID: " . $result['id'] . "\n";
    } else {
        echo "✗ " . $result['name'] . " - " . $result['error'] . "\n";
    }
}

echo "\n" . str_repeat("-", 60) . "\n\n";

// Step 3: Get BigCommerce variants by SKU (from WordPress data)
echo "3. Finding BigCommerce variants by SKU...\n";

// Example SKUs from your WordPress variation data
$wordpress_skus = ['67003.08', '67002.08']; // Unscented and Lemon Verbena variants
$bigcommerce_variants = [];

foreach ($wordpress_skus as $sku) {
    $variant_id = getBigCommerceVariantBySku($sku);
    if ($variant_id) {
        $bigcommerce_variants[$sku] = $variant_id;
        echo "✓ Found BigCommerce variant for SKU $sku: ID $variant_id\n";
    } else {
        echo "✗ No BigCommerce variant found for SKU $sku\n";
    }
}

echo "\n" . str_repeat("-", 60) . "\n\n";

// Step 4: Assign pricing to variants (if we have price lists and variants)
if (!empty($created_price_lists) && !empty($bigcommerce_variants)) {
    echo "4. Assigning pricing to BigCommerce variants...\n";
    
    // Get the created price list IDs
    $wholesale_id = $created_price_lists['wholesale']['success'] ? $created_price_lists['wholesale']['id'] : null;
    $canada_wholesale_id = $created_price_lists['canada_wholesale']['success'] ? $created_price_lists['canada_wholesale']['id'] : null;
    
    if ($wholesale_id) {
        echo "\nAssigning Wholesale pricing (Price List ID: $wholesale_id)...\n";
        
        foreach ($bigcommerce_variants as $sku => $variant_id) {
            $wholesale_price = 12.00; // Example wholesale price
            $result = assignBigCommercePriceToVariant($wholesale_id, $variant_id, $wholesale_price);
            
            if ($result) {
                echo "✓ Assigned wholesale price $$wholesale_price to variant $variant_id (SKU: $sku)\n";
            } else {
                echo "✗ Failed to assign wholesale price to variant $variant_id (SKU: $sku)\n";
            }
        }
    }
    
    if ($canada_wholesale_id) {
        echo "\nAssigning Canada Wholesale pricing (Price List ID: $canada_wholesale_id)...\n";
        
        foreach ($bigcommerce_variants as $sku => $variant_id) {
            $canada_price = 13.50; // Example Canada wholesale price
            $result = assignBigCommercePriceToVariant($canada_wholesale_id, $variant_id, $canada_price);
            
            if ($result) {
                echo "✓ Assigned Canada wholesale price $$canada_price to variant $variant_id (SKU: $sku)\n";
            } else {
                echo "✗ Failed to assign Canada wholesale price to variant $variant_id (SKU: $sku)\n";
            }
        }
    }
} else {
    echo "4. Skipping pricing assignment - missing price lists or variants\n";
}

echo "\n" . str_repeat("-", 60) . "\n\n";

// Step 5: Batch pricing example
if (!empty($created_price_lists) && !empty($bigcommerce_variants)) {
    echo "5. Demonstrating batch pricing assignment...\n";
    
    $wholesale_id = $created_price_lists['wholesale']['success'] ? $created_price_lists['wholesale']['id'] : null;
    
    if ($wholesale_id) {
        // Prepare batch pricing data
        $variant_prices = [];
        foreach ($bigcommerce_variants as $sku => $variant_id) {
            $variant_prices[$variant_id] = 11.99; // Batch wholesale price
        }
        
        $batch_result = batchAssignBigCommercePrices($wholesale_id, $variant_prices);
        
        if ($batch_result) {
            echo "✓ Batch pricing assignment successful\n";
            echo "- Price List ID: " . $batch_result['price_list_id'] . "\n";
            echo "- Total Records: " . $batch_result['total_records'] . "\n";
        } else {
            echo "✗ Batch pricing assignment failed\n";
        }
    }
} else {
    echo "5. Skipping batch pricing - missing requirements\n";
}

echo "\n" . str_repeat("-", 60) . "\n\n";

// Step 6: Verify price records
echo "6. Verifying created price records...\n";

if (!empty($created_price_lists)) {
    foreach ($created_price_lists as $type => $price_list_info) {
        if ($price_list_info['success']) {
            $price_list_id = $price_list_info['id'];
            $price_list_name = $price_list_info['name'];
            
            echo "\nChecking price records for $price_list_name (ID: $price_list_id)...\n";
            $price_records = getBigCommercePriceRecords($price_list_id);
            
            if ($price_records) {
                echo "✓ Found " . count($price_records) . " price records:\n";
                foreach ($price_records as $record) {
                    echo "- Variant ID: " . $record['variant_id'] . ", Price: $" . $record['price'] . "\n";
                }
            } else {
                echo "No price records found or API error\n";
            }
        }
    }
}

echo "\n=== BigCommerce Price Lists Example Complete ===\n";

echo "\nNext Steps:\n";
echo "1. Check your BigCommerce admin panel to see the created price lists\n";
echo "2. Assign the price lists to customer groups in BigCommerce\n";
echo "3. Test the pricing on your BigCommerce storefront\n";
echo "4. Use the batch functions for efficient bulk pricing updates\n";

?>
