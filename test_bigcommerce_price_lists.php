<?php
/**
 * Test script for BigCommerce price list functions
 * 
 * This script tests all the BigCommerce price list functionality
 * to ensure everything works correctly.
 */

include("config/config.php");

echo "=== BigCommerce Price Lists Test ===\n\n";

// Test 1: Get existing price lists
echo "1. Testing getBigCommercePriceLists()...\n";
$existing_lists = getBigCommercePriceLists();

if ($existing_lists !== false) {
    echo "✓ Successfully retrieved price lists\n";
    echo "- Found " . count($existing_lists) . " existing price lists\n";
    
    if (!empty($existing_lists)) {
        echo "- Existing price lists:\n";
        foreach ($existing_lists as $list) {
            echo "  * ID: " . $list['id'] . ", Name: " . $list['name'] . "\n";
        }
    }
} else {
    echo "✗ Failed to retrieve price lists\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 2: Create a test price list
echo "2. Testing createBigCommercePriceList()...\n";
$test_price_list_data = [
    'name' => 'Test Wholesale List - ' . date('Y-m-d H:i:s'),
    'active' => true
];

$created_price_list_id = createBigCommercePriceList($test_price_list_data);

if ($created_price_list_id) {
    echo "✓ Successfully created test price list\n";
    echo "- Created Price List ID: $created_price_list_id\n";
} else {
    echo "✗ Failed to create test price list\n";
    echo "Skipping remaining tests that depend on created price list\n";
    exit(1);
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 3: Get the created price list
echo "3. Testing getBigCommercePriceList()...\n";
$retrieved_price_list = getBigCommercePriceList($created_price_list_id);

if ($retrieved_price_list) {
    echo "✓ Successfully retrieved price list\n";
    echo "- ID: " . $retrieved_price_list['id'] . "\n";
    echo "- Name: " . $retrieved_price_list['name'] . "\n";
    echo "- Active: " . ($retrieved_price_list['active'] ? 'Yes' : 'No') . "\n";
} else {
    echo "✗ Failed to retrieve price list\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 4: Update the price list
echo "4. Testing updateBigCommercePriceList()...\n";
$update_data = [
    'name' => 'Updated Test Wholesale List - ' . date('Y-m-d H:i:s'),
    'active' => true
];

$update_result = updateBigCommercePriceList($created_price_list_id, $update_data);

if ($update_result) {
    echo "✓ Successfully updated price list\n";
    
    // Verify the update
    $updated_list = getBigCommercePriceList($created_price_list_id);
    if ($updated_list && $updated_list['name'] === $update_data['name']) {
        echo "✓ Update verified - name changed successfully\n";
    } else {
        echo "? Update may not have taken effect immediately\n";
    }
} else {
    echo "✗ Failed to update price list\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 5: Test variant lookup by SKU
echo "5. Testing getBigCommerceVariantBySku()...\n";
$test_skus = ['67003.08', '67002.08']; // From your WordPress data
$found_variants = [];

foreach ($test_skus as $sku) {
    $variant_id = getBigCommerceVariantBySku($sku);
    if ($variant_id) {
        $found_variants[$sku] = $variant_id;
        echo "✓ Found variant for SKU $sku: ID $variant_id\n";
    } else {
        echo "? No variant found for SKU $sku (may not exist in BigCommerce)\n";
    }
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 6: Test price assignment (only if we found variants)
if (!empty($found_variants)) {
    echo "6. Testing assignBigCommercePriceToVariant()...\n";
    
    $test_variant_id = array_values($found_variants)[0]; // Get first variant
    $test_price = 15.99;
    
    $price_assignment_result = assignBigCommercePriceToVariant($created_price_list_id, $test_variant_id, $test_price);
    
    if ($price_assignment_result) {
        echo "✓ Successfully assigned price $$test_price to variant $test_variant_id\n";
    } else {
        echo "✗ Failed to assign price to variant\n";
    }
} else {
    echo "6. Skipping price assignment test - no variants found\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 7: Test getting price records
echo "7. Testing getBigCommercePriceRecords()...\n";
$price_records = getBigCommercePriceRecords($created_price_list_id);

if ($price_records !== false) {
    echo "✓ Successfully retrieved price records\n";
    echo "- Found " . count($price_records) . " price records\n";
    
    if (!empty($price_records)) {
        echo "- Price records:\n";
        foreach ($price_records as $record) {
            echo "  * Variant ID: " . $record['variant_id'] . ", Price: $" . $record['price'] . "\n";
        }
    }
} else {
    echo "✗ Failed to retrieve price records\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 8: Test batch pricing (if we have variants)
if (!empty($found_variants)) {
    echo "8. Testing batchAssignBigCommercePrices()...\n";
    
    $batch_prices = [];
    foreach ($found_variants as $sku => $variant_id) {
        $batch_prices[$variant_id] = 14.99; // Batch price
    }
    
    $batch_result = batchAssignBigCommercePrices($created_price_list_id, $batch_prices);
    
    if ($batch_result) {
        echo "✓ Successfully assigned batch prices\n";
        echo "- Total records: " . $batch_result['total_records'] . "\n";
        echo "- Price list ID: " . $batch_result['price_list_id'] . "\n";
    } else {
        echo "✗ Failed to assign batch prices\n";
    }
} else {
    echo "8. Skipping batch pricing test - no variants found\n";
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 9: Clean up - delete the test price list
echo "9. Testing deleteBigCommercePriceList() (cleanup)...\n";
$delete_result = deleteBigCommercePriceList($created_price_list_id);

if ($delete_result) {
    echo "✓ Successfully deleted test price list\n";
    
    // Verify deletion
    $deleted_list = getBigCommercePriceList($created_price_list_id);
    if (!$deleted_list) {
        echo "✓ Deletion verified - price list no longer exists\n";
    } else {
        echo "? Price list may still exist (deletion may take time)\n";
    }
} else {
    echo "✗ Failed to delete test price list\n";
    echo "! Manual cleanup may be required for price list ID: $created_price_list_id\n";
}

echo "\n=== BigCommerce Price Lists Test Complete ===\n";

echo "\nTest Summary:\n";
echo "- All core price list functions have been tested\n";
echo "- Check the results above for any failures\n";
echo "- If tests passed, the BigCommerce price list integration is working\n";
echo "- You can now use these functions to create and manage price lists\n";

?>
