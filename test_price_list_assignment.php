<?php
/**
 * Test script for price list assignment functions
 * 
 * This script tests the assignVariantPriceList function with the existing
 * product and variation data from your WordPress site.
 */

include("config/config.php");

echo "=== Price List Assignment Test ===\n\n";

// Test data based on your provided variation response
$test_product_id = 104733;  // Bad Ace Soap product
$test_variant_id = 104738;  // Unscented variant

echo "Testing with:\n";
echo "- Product ID: $test_product_id\n";
echo "- Variant ID: $test_variant_id\n\n";

// Test 1: Get current variation data
echo "1. Getting current variation data...\n";
$current_data = getWooProductVariation($test_product_id, $test_variant_id);

if ($current_data) {
    echo "✓ Successfully retrieved variation data\n";
    echo "- SKU: " . $current_data['sku'] . "\n";
    echo "- Regular Price: $" . $current_data['regular_price'] . "\n";
    
    // Check current wholesale pricing
    foreach ($current_data['meta_data'] as $meta) {
        if ($meta['key'] === 'wholesale_multi_user_pricing') {
            $wholesale_pricing = $meta['value'];
            echo "- Current wholesale pricing found:\n";
            
            if (isset($wholesale_pricing[453])) {  // Wholesale
                echo "  * Wholesale: $" . $wholesale_pricing[453][$test_variant_id]['wholesaleprice'] . "\n";
            }
            
            if (isset($wholesale_pricing[455])) {  // Canada Wholesale
                echo "  * Canada Wholesale: $" . $wholesale_pricing[455][$test_variant_id]['wholesaleprice'] . "\n";
            }
            break;
        }
    }
} else {
    echo "✗ Failed to retrieve variation data\n";
    exit(1);
}

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 2: Assign Wholesale pricing (Price List ID: 2)
echo "2. Testing Wholesale price assignment (Price List ID: 2)...\n";
$new_wholesale_price = 10.50;

$result = assignVariantPriceList($test_product_id, $test_variant_id, 2, $new_wholesale_price);

if ($result) {
    echo "✓ Successfully assigned Wholesale price of $$new_wholesale_price\n";
} else {
    echo "✗ Failed to assign Wholesale price\n";
}

echo "\n" . str_repeat("-", 30) . "\n\n";

// Test 3: Assign Canada Wholesale pricing (Price List ID: 3)
echo "3. Testing Canada Wholesale price assignment (Price List ID: 3)...\n";
$new_canada_price = 11.25;

$result = assignVariantPriceList($test_product_id, $test_variant_id, 3, $new_canada_price);

if ($result) {
    echo "✓ Successfully assigned Canada Wholesale price of $$new_canada_price\n";
} else {
    echo "✗ Failed to assign Canada Wholesale price\n";
}

echo "\n" . str_repeat("-", 30) . "\n\n";

// Test 4: Verify the changes
echo "4. Verifying the price changes...\n";
$updated_data = getWooProductVariation($test_product_id, $test_variant_id);

if ($updated_data) {
    echo "✓ Retrieved updated variation data\n";
    
    foreach ($updated_data['meta_data'] as $meta) {
        if ($meta['key'] === 'wholesale_multi_user_pricing') {
            $wholesale_pricing = $meta['value'];
            echo "Updated wholesale pricing:\n";
            
            if (isset($wholesale_pricing[453])) {  // Wholesale
                $actual_wholesale = $wholesale_pricing[453][$test_variant_id]['wholesaleprice'];
                echo "- Wholesale: $$actual_wholesale";
                if ($actual_wholesale == number_format($new_wholesale_price, 2, '.', '')) {
                    echo " ✓\n";
                } else {
                    echo " ✗ (Expected: $$new_wholesale_price)\n";
                }
            }
            
            if (isset($wholesale_pricing[455])) {  // Canada Wholesale
                $actual_canada = $wholesale_pricing[455][$test_variant_id]['wholesaleprice'];
                echo "- Canada Wholesale: $$actual_canada";
                if ($actual_canada == number_format($new_canada_price, 2, '.', '')) {
                    echo " ✓\n";
                } else {
                    echo " ✗ (Expected: $$new_canada_price)\n";
                }
            }
            break;
        }
    }
} else {
    echo "✗ Failed to retrieve updated variation data\n";
}

echo "\n" . str_repeat("-", 30) . "\n\n";

// Test 5: Test error handling with invalid price list ID
echo "5. Testing error handling with invalid price list ID...\n";
$result = assignVariantPriceList($test_product_id, $test_variant_id, 999, 15.00);

if ($result === false) {
    echo "✓ Correctly handled invalid price list ID\n";
} else {
    echo "✗ Should have failed with invalid price list ID\n";
}

echo "\n=== Test Complete ===\n";

?>
