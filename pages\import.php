<?php
include("../config/config.php");


$getWpProducts = getWpProducts();

// pre($getWpProducts); exit;

foreach($getWpProducts as $product){
	if($product['id']== 104733){
		$productData = array();

		pre($product['id']);
		$productData['sku']							= 	$product['sku'];
		$productData['type']						= 	'physical';				
		$productData['name']						=   $product['name'];
		$productData['price']						= 	$product['price'];		
		$productData['weight']						= 	$product['weight']?$product['weight'] : 0;
		$productData['description'] 				= 	$product['short_description'].''.$product['description'];

		if(isset($product['dimensions']['length']) && $product['dimensions']['length'] !=""){
			$productData['length'] =	$product['dimensions']['length'];
		}
		if(isset($product['dimensions']['depth']) && $product['dimensions']['depth'] !=""){
			$productData['depth'] =	$product['dimensions']['depth'];
		}
		if(isset($product['dimensions']['height']) && $product['dimensions']['height'] !=""){
			$productData['height'] =	$product['dimensions']['height'];
		}
		$productData['is_visible'] = $product['status'] == 'publish'  ? true : false;

	// pre($productData);
		$categoryArray = [];

		foreach($product['categories'] as $cat){
			$cat_id = getCategoriesByName($cat['name']);
			if($cat_id){
				array_push($categoryArray, $cat_id);
			}
		}

		$productData['categories'] = $categoryArray;

		if(!empty($product['images'])){
			$i =0;
			$imgdata = [];

			foreach($product['images'] as $images){
				$imgdata[] = [
					"image_url"  => "https://upload.wikimedia.org/wikipedia/commons/thumb/a/a9/Example.jpg/800px-Example.jpg",
					"is_thumbnail" => $i === 0,
				];
				$i++;			
			}
			if (!empty($imgdata)) {
				$productData['images'] = $imgdata;
			}
		}
		// pre($productData);
		$product_id = createProduct($productData);
		// $product_id = 2952899;
		if($product_id)	{
			m_log("created product - ".$product_id);

			foreach ($product['attributes'] as $attr) {

				$opdata = array();				
				$i=0;
				foreach($attr['options'] as $options){

					$opdata[$i]['label'] = $options;
					if($i==0){
						$opdata[$i]['is_default'] = true;	
					}					
					$i++;
				}
				$productVariants['product_id'] = $product_id;
				$productVariants['display_name'] = $attr['name'];
				$productVariants['type'] = 'rectangles';				
				$productVariants['option_values'] = 	$opdata;

				// pre($productVariants);
				createVariants($product_id,$productVariants);
			}




					//////


			$woo_variants = getWooProductVariants($product['id']); // Get WooCommerce variations;

$options = getProductOptions($product_id); // Get BigCommerce product options
// pre($woo_variants);
// pre($options);
// exit;
foreach ($woo_variants as $variant) {
	$variantData = [
		"sku" => $variant['sku'],
		"price" => (float)$variant['price'],
		"weight" => (float)$variant['weight'],
		"is_visible" => true,
		"inventory_level" => $variant['stock_quantity'] ?? 0,
		"option_values" => []
	];

    // Optional: dimensions
	if (!empty($variant['dimensions'])) {
		if (!empty($variant['dimensions']['width'])) $variantData['width'] = (float)$variant['dimensions']['width'];
		if (!empty($variant['dimensions']['height'])) $variantData['height'] = (float)$variant['dimensions']['height'];
		if (!empty($variant['dimensions']['length'])) $variantData['depth'] = (float)$variant['dimensions']['length'];
	}

    // Optional: image
	if (!empty($variant['image']['src'])) {
		$variantData['image_url'] = $variant['image']['src'];
	}

    // Match Woo attribute to BigCommerce option
	foreach ($variant['attributes'] as $attr) {
		$wooOptionName = $attr['name'];
		$wooOptionValue = $attr['option'];

		foreach ($options as $option) {
			if (strcasecmp($option['display_name'], $wooOptionName) === 0) {
                // Match found, now find the correct value
				foreach ($option['option_values'] as $optValue) {
					if (strcasecmp($optValue['label'], $wooOptionValue) === 0) {
						$variantData['option_values'][] = [
							"option_id" => $option['id'],
							"id" => $optValue['id'],
							"label" => $optValue['label']
						];
						break;
					}
				}
			}
		}
	}

    // Send variant to BigCommerce
	pre($variantData); 
	$response = createVariantsData($product_id, $variantData);



}






			///////////






}
}
}

exit;
