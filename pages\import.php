<?php
include("../config/config.php");

$getWpProducts = getWpProducts();

foreach($getWpProducts as $product){
	if($product['id']== 72973){  // Change this ID to process different products, or remove condition to process all
		$productData = array();

		// pre($product['id']);
		$productData['sku']							= 	$product['sku'];
		$productData['type']						= 	'physical';
		$productData['name']						=   $product['name'];
		$productData['price']						= 	$product['price'];
		$productData['weight']						= 	$product['weight']?$product['weight'] : 0;
		$productData['description'] 				= 	$product['short_description'].''.$product['description'];

		if(isset($product['dimensions']['length']) && $product['dimensions']['length'] !=""){
			$productData['length'] =	$product['dimensions']['length'];
		}
		if(isset($product['dimensions']['depth']) && $product['dimensions']['depth'] !=""){
			$productData['depth'] =	$product['dimensions']['depth'];
		}
		if(isset($product['dimensions']['height']) && $product['dimensions']['height'] !=""){
			$productData['height'] =	$product['dimensions']['height'];
		}
		$productData['is_visible'] = $product['status'] == 'publish'  ? true : false;

	// pre($productData);
		$categoryArray = [];

		foreach($product['categories'] as $cat){
			$cat_id = getCategoriesByName($cat['name']);
			if($cat_id){
				array_push($categoryArray, $cat_id);
			}
		}

		$productData['categories'] = $categoryArray;

		if(!empty($product['images'])){
			$i =0;
			$imgdata = [];

			foreach($product['images'] as $images){
				$imgdata[] = [
					"image_url"  => $images['src'], // Use actual image URL
					"is_thumbnail" => $i === 0,
				];
				$i++;
			}
			if (!empty($imgdata)) {
				// $productData['images'] = $imgdata;
			}
		}
		echo $product_id = createProduct($productData);
		if($product_id)	{
			m_log("created product - ".$product_id);

			foreach ($product['attributes'] as $attr) {

				$opdata = array();				
				$i=0;
				foreach($attr['options'] as $options){

					$opdata[$i]['label'] = $options;
					if($i==0){
						$opdata[$i]['is_default'] = true;	
					}					
					$i++;
				}
				$productVariants['product_id'] = $product_id;
				$productVariants['display_name'] = $attr['name'];
				$productVariants['type'] = 'rectangles';				
				$productVariants['option_values'] = 	$opdata;

				createVariants($product_id,$productVariants);
			}

			$woo_variants = getWooProductVariants($product['id']); // Get WooCommerce variations;
			$options = getProductOptions($product_id); // Get BigCommerce product options
			
			// Initialize pricing sync tracking
			$pricing_sync_stats = [
				'total_variants' => 0,
				'variants_with_pricing' => 0,
				'wholesale_synced' => 0,
				'canada_wholesale_synced' => 0,
				'quantity_break_variants' => 0,
				'pricing_errors' => 0
			];
			
			echo "Processing " . count($woo_variants) . " variants...\n";

			foreach ($woo_variants as $variant) {
				$pricing_sync_stats['total_variants']++;

				$variantData = [
					"sku" => $variant['sku'],
					"price" => (float)$variant['price'],
					"weight" => (float)$variant['weight'],
					"is_visible" => true,
					"inventory_level" => $variant['stock_quantity'] ?? 0,
					"option_values" => []
				];

				// Optional: dimensions
				if (!empty($variant['dimensions'])) {
					if (!empty($variant['dimensions']['width'])) $variantData['width'] = (float)$variant['dimensions']['width'];
					if (!empty($variant['dimensions']['height'])) $variantData['height'] = (float)$variant['dimensions']['height'];
					if (!empty($variant['dimensions']['length'])) $variantData['depth'] = (float)$variant['dimensions']['length'];
				}

				// Optional: image
				if (!empty($variant['image']['src'])) {
					$variantData['image_url'] = $variant['image']['src'];
				}

				// Match Woo attribute to BigCommerce option
				$matched_options = 0;
				foreach ($variant['attributes'] as $attr) {
					$wooOptionName = $attr['name'];
					$wooOptionValue = $attr['option'];

					foreach ($options as $option) {
						if (strcasecmp($option['display_name'], $wooOptionName) === 0) {
							// Match found, now find the correct value
							foreach ($option['option_values'] as $optValue) {
								if (strcasecmp($optValue['label'], $wooOptionValue) === 0) {
									$variantData['option_values'][] = [
										"option_id" => $option['id'],
										"id" => $optValue['id'],
										"label" => $optValue['label']
									];
									$matched_options++;
									break;
								}
							}
							break;
						}
					}
				}

				// Only create variant if all attributes were matched
				if ($matched_options !== count($variant['attributes'])) {
					echo "✗ Skipping variant {$variant['sku']} - attribute mismatch\n";
					continue;
				}

				// Send variant to BigCommerce
				$response = createVariantsData($product_id, $variantData);

				// If variant creation failed due to image error, retry without image
				if (!$response && isset($variantData['image_url'])) {
					unset($variantData['image_url']);
					$response = createVariantsData($product_id, $variantData);
				}

				// Price List Synchronization - Added functionality
				if ($response && isset($response->id)) {
					$bc_variant_id = $response->id;
					echo "✓ Created variant: {$variant['sku']} (BC ID: $bc_variant_id)\n";
					
					// Sync wholesale pricing (supports both single pricing and quantity breaks)
					$pricing_synced = false;

					// Check if this variant has quantity break pricing
					$wholesale_quantity_breaks = extractWholesaleQuantityBreaks($variant, 453);
					$canada_quantity_breaks = extractWholesaleQuantityBreaks($variant, 455);

					if (($wholesale_quantity_breaks && count($wholesale_quantity_breaks) > 1) ||
						($canada_quantity_breaks && count($canada_quantity_breaks) > 1)) {
						$pricing_sync_stats['quantity_break_variants']++;
					}

					// Wholesale price list (ID: 2, WordPress role ID: 453)
					$wholesale_sync_result = syncWholesalePricingToVariant(2, $bc_variant_id, $variant, 453);
					if ($wholesale_sync_result) {
						$pricing_sync_stats['wholesale_synced']++;
						$pricing_synced = true;
					} else {
						$pricing_sync_stats['pricing_errors']++;
					}

					// Canada Wholesale price list (ID: 3, WordPress role ID: 455)
					$canada_wholesale_sync_result = syncWholesalePricingToVariant(3, $bc_variant_id, $variant, 455);
					if ($canada_wholesale_sync_result) {
						$pricing_sync_stats['canada_wholesale_synced']++;
						$pricing_synced = true;
					} else {
						$pricing_sync_stats['pricing_errors']++;
					}

					if ($pricing_synced) {
						$pricing_sync_stats['variants_with_pricing']++;
					}
				} else {
					echo "✗ Failed to create variant: {$variant['sku']}\n";
				}
			}

			// Display pricing sync summary
			m_log("PRICING SYNC SUMMARY for Product: {$product['name']}");
			m_log("Total Variants Processed: {$pricing_sync_stats['total_variants']}");
			m_log("Variants with Pricing Data: {$pricing_sync_stats['variants_with_pricing']}");
			m_log("Variants with Quantity Break Pricing: {$pricing_sync_stats['quantity_break_variants']}");
			m_log("Wholesale Prices Synced: {$pricing_sync_stats['wholesale_synced']}");
			m_log("Canada Wholesale Prices Synced: {$pricing_sync_stats['canada_wholesale_synced']}");
			m_log("Pricing Sync Errors: {$pricing_sync_stats['pricing_errors']}");

			$total_synced = $pricing_sync_stats['wholesale_synced'] + $pricing_sync_stats['canada_wholesale_synced'];
			$success_rate = $pricing_sync_stats['variants_with_pricing'] > 0 ?
				round(($total_synced / ($pricing_sync_stats['variants_with_pricing'] * 2)) * 100, 1) : 0;
			m_log("Pricing Sync Success Rate: {$success_rate}%");
		}
	}
}

exit;
