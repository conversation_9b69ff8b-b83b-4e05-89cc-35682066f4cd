<?php
/**
 * Enhanced Product Import System
 *
 * This script has been enhanced to support both the original manual import process
 * and the new dynamic import system. Choose your import method below.
 */

include("../config/config.php");

// Import Configuration
$import_mode = 'dynamic'; // Options: 'manual', 'dynamic'
$specific_product_id = 104733; // Set to null to process all products

echo "=== ENHANCED PRODUCT IMPORT SYSTEM ===\n";
echo "Import Mode: " . strtoupper($import_mode) . "\n";
echo "Target Product: " . ($specific_product_id ? $specific_product_id : 'All products') . "\n\n";

if ($import_mode === 'dynamic') {
    // Use the new dynamic import system
    echo "Using Dynamic Import System...\n\n";

    $getWpProducts = getWpProducts();

    if (!$getWpProducts) {
        die("Failed to fetch WordPress products\n");
    }

    // Filter to specific product if set
    if ($specific_product_id) {
        $getWpProducts = array_filter($getWpProducts, function($product) use ($specific_product_id) {
            return $product['id'] == $specific_product_id;
        });
    }

    foreach ($getWpProducts as $product) {
        // Use the dynamic import function
        $import_result = dynamicProductImport($product, [
            'price_list_config' => [
                'wholesale' => [
                    'bc_price_list_id' => 2,
                    'name' => 'Wholesale',
                    'wp_role_id' => 453
                ],
                'canada_wholesale' => [
                    'bc_price_list_id' => 3,
                    'name' => 'Canada Wholesale',
                    'wp_role_id' => 455
                ]
            ]
        ]);

        // Display results
        if ($import_result['success']) {
            echo "\n✓ SUCCESSFULLY IMPORTED: {$import_result['wp_product_name']}\n";
            echo "  - BigCommerce Product ID: {$import_result['bc_product_id']}\n";
            echo "  - Created Options: " . count($import_result['created_options']) . "\n";
            echo "  - Created Variants: " . count($import_result['created_variants']) . "\n";

            if (!empty($import_result['pricing_sync_results'])) {
                echo "  - Price List Sync Results:\n";
                foreach ($import_result['pricing_sync_results'] as $type => $sync_result) {
                    $status = $sync_result['success'] ? '✓' : '✗';
                    echo "    $status $type: {$sync_result['count']} prices synced\n";
                }
            }
        } else {
            echo "\n✗ FAILED TO IMPORT: {$import_result['wp_product_name']}\n";
            if (!empty($import_result['errors'])) {
                foreach ($import_result['errors'] as $error) {
                    echo "  - Error: $error\n";
                }
            }
        }

        // Add delay to avoid API rate limits
        sleep(2);
    }

} else {
    // Original manual import system (preserved for backward compatibility)
    echo "Using Manual Import System...\n\n";

    $getWpProducts = getWpProducts();

    foreach($getWpProducts as $product){
        if($product['id']== $specific_product_id){
            $productData = array();

            pre($product['id']);
            $productData['sku']							= 	$product['sku'];
            $productData['type']						= 	'physical';
            $productData['name']						=   $product['name'];
            $productData['price']						= 	$product['price'];
            $productData['weight']						= 	$product['weight']?$product['weight'] : 0;
            $productData['description'] 				= 	$product['short_description'].''.$product['description'];

            if(isset($product['dimensions']['length']) && $product['dimensions']['length'] !=""){
                $productData['length'] =	$product['dimensions']['length'];
            }
            if(isset($product['dimensions']['depth']) && $product['dimensions']['depth'] !=""){
                $productData['depth'] =	$product['dimensions']['depth'];
            }
            if(isset($product['dimensions']['height']) && $product['dimensions']['height'] !=""){
                $productData['height'] =	$product['dimensions']['height'];
            }
            $productData['is_visible'] = $product['status'] == 'publish'  ? true : false;

        // pre($productData);
            $categoryArray = [];

            foreach($product['categories'] as $cat){
                $cat_id = getCategoriesByName($cat['name']);
                if($cat_id){
                    array_push($categoryArray, $cat_id);
                }
            }

            $productData['categories'] = $categoryArray;

            if(!empty($product['images'])){
                $i =0;
                $imgdata = [];

                foreach($product['images'] as $images){
                    $imgdata[] = [
                        "image_url"  => $images['src'], // Use actual image URL
                        "is_thumbnail" => $i === 0,
                    ];
                    $i++;
                }
                if (!empty($imgdata)) {
                    $productData['images'] = $imgdata;
                }
            }
            // pre($productData);
            $product_id = createProduct($productData);
            // $product_id = 2952899;
            if($product_id)	{
                m_log("created product - ".$product_id);

			foreach ($product['attributes'] as $attr) {

				$opdata = array();				
				$i=0;
				foreach($attr['options'] as $options){

					$opdata[$i]['label'] = $options;
					if($i==0){
						$opdata[$i]['is_default'] = true;	
					}					
					$i++;
				}
				$productVariants['product_id'] = $product_id;
				$productVariants['display_name'] = $attr['name'];
				$productVariants['type'] = 'rectangles';				
				$productVariants['option_values'] = 	$opdata;

				// pre($productVariants);
				createVariants($product_id,$productVariants);
			}




					//////


			$woo_variants = getWooProductVariants($product['id']); // Get WooCommerce variations;

$options = getProductOptions($product_id); // Get BigCommerce product options
// pre($woo_variants);
// pre($options);
// exit;
foreach ($woo_variants as $variant) {
	$variantData = [
		"sku" => $variant['sku'],
		"price" => (float)$variant['price'],
		"weight" => (float)$variant['weight'],
		"is_visible" => true,
		"inventory_level" => $variant['stock_quantity'] ?? 0,
		"option_values" => []
	];

    // Optional: dimensions
	if (!empty($variant['dimensions'])) {
		if (!empty($variant['dimensions']['width'])) $variantData['width'] = (float)$variant['dimensions']['width'];
		if (!empty($variant['dimensions']['height'])) $variantData['height'] = (float)$variant['dimensions']['height'];
		if (!empty($variant['dimensions']['length'])) $variantData['depth'] = (float)$variant['dimensions']['length'];
	}

    // Optional: image
	if (!empty($variant['image']['src'])) {
		$variantData['image_url'] = $variant['image']['src'];
	}

    // Match Woo attribute to BigCommerce option
	foreach ($variant['attributes'] as $attr) {
		$wooOptionName = $attr['name'];
		$wooOptionValue = $attr['option'];

		foreach ($options as $option) {
			if (strcasecmp($option['display_name'], $wooOptionName) === 0) {
                // Match found, now find the correct value
				foreach ($option['option_values'] as $optValue) {
					if (strcasecmp($optValue['label'], $wooOptionValue) === 0) {
						$variantData['option_values'][] = [
							"option_id" => $option['id'],
							"id" => $optValue['id'],
							"label" => $optValue['label']
						];
						break;
					}
				}
			}
		}
	}

    // Send variant to BigCommerce
	// pre($variantData); 
	$response = createVariantsData($product_id, $variantData);



}






			///////////






}
}
}

exit;
