<?php

function getCategoriesByName($name){
	
	$name = str_replace(' ', '%20', $name);
	$name = str_replace('&', '%26', $name);
	$name = str_replace('amp;', '', $name);
	$url = "catalog/trees/categories?name=".trim($name);	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response);
	// pre($result);
	if(!empty($result->data)){
		return $result->data[0]->category_id;	
	} else {
		return false;
	}	
}

function createCategory($data){
	// pre($data);
	$url = "catalog/trees/categories";	
	$curl_response =  curl($url,'POST',$data);
	$result = json_decode($curl_response);
	// pre($result);
	// exit;
	
	if(isset($result->data)){					
		return $result->data[0]->category_id;
	} else {
		return false;
	}	
}

function getBrandByName($name){
	$name = str_replace(" ", "%20", $name);
	$url = "catalog/brands?name=".$name;	
	$curl_response =  curl($url,'GET');
	$result = json_decode($curl_response);	
	
	if(!empty($result->data)){
		return $result->data[0]->id;	
	} else {
		return false;
	}	
}
function createBrand($data){
	$url = "catalog/brands";	
	$curl_response =  curl($url,'POST',$data);
	$result = json_decode($curl_response);

	if(isset($result->data)){					
		return $result->data->id;
	} else {
		return false;
	}	
}
function updateProduct($id,$data){
	$url = "catalog/products/".$id;	
	$curl_response =  curl($url,'PUT',$data);	
	if($curl_response){				
		return true;
	} else {
		return false;
	}	
}

?>