<?php
/**
 * <PERSON><PERSON><PERSON> to check if pricing changes are visible on the frontend
 * and provide troubleshooting steps
 */

include("config/config.php");

echo "=== Frontend Pricing Check ===\n\n";

$product_id = 104733;
$variant_id = 104738;

echo "1. Checking WordPress product data...\n";

// Get the main product data
$product_url = WP_URL . "products/$product_id?consumer_key=" . COMSUMER_KEY . "&consumer_secret=" . COMSUMER_SECRET;
$product_response = wp_curl($product_url);

if ($product_response) {
    $product_data = json_decode($product_response, true);
    echo "✓ Product data retrieved\n";
    echo "- Product Name: " . $product_data['name'] . "\n";
    echo "- Product Status: " . $product_data['status'] . "\n";
    echo "- Product Type: " . $product_data['type'] . "\n";
    echo "- Date Modified: " . $product_data['date_modified'] . "\n";
} else {
    echo "✗ Failed to get product data\n";
}

echo "\n2. Checking variation data...\n";
$variation_url = WP_URL . "products/$product_id/variations/$variant_id?consumer_key=" . COMSUMER_KEY . "&consumer_secret=" . COMSUMER_SECRET;
$variation_response = wp_curl($variation_url);

if ($variation_response) {
    $variation_data = json_decode($variation_response, true);
    echo "✓ Variation data retrieved\n";
    echo "- Variation Status: " . $variation_data['status'] . "\n";
    echo "- Regular Price: $" . $variation_data['regular_price'] . "\n";
    echo "- Sale Price: " . ($variation_data['sale_price'] ?: 'None') . "\n";
    echo "- Date Modified: " . $variation_data['date_modified'] . "\n";
} else {
    echo "✗ Failed to get variation data\n";
}

echo "\n3. Checking wholesale plugin meta fields...\n";

// Check for wholesale plugin specific meta fields
$wholesale_fields_to_check = [
    'wholesale_multi_user_pricing',
    'product_tier_pricing',
    '_wholesale_price',
    '_wholesale_customer_price',
    'wholesale_price'
];

$found_wholesale_fields = [];

if (isset($variation_data['meta_data'])) {
    foreach ($variation_data['meta_data'] as $meta) {
        if (in_array($meta['key'], $wholesale_fields_to_check)) {
            $found_wholesale_fields[] = $meta['key'];
            echo "✓ Found: " . $meta['key'] . "\n";
            
            if ($meta['key'] === 'wholesale_multi_user_pricing') {
                $pricing = $meta['value'];
                echo "  - Wholesale roles configured: " . implode(', ', array_keys($pricing)) . "\n";
                
                foreach ($pricing as $role_id => $role_data) {
                    $role_name = isset($role_data['slug']) ? $role_data['slug'] : "Role $role_id";
                    echo "  - $role_name: ";
                    
                    if (isset($role_data[$variant_id])) {
                        echo "$" . $role_data[$variant_id]['wholesaleprice'] . "\n";
                    } else {
                        echo "No price set\n";
                    }
                }
            }
        }
    }
}

if (empty($found_wholesale_fields)) {
    echo "✗ No wholesale pricing fields found\n";
}

echo "\n4. Checking if wholesale plugin is active...\n";

// Try to detect wholesale plugin by checking for specific meta patterns
$has_wholesale_plugin = false;
if (isset($variation_data['meta_data'])) {
    foreach ($variation_data['meta_data'] as $meta) {
        if (strpos($meta['key'], 'wholesale') !== false) {
            $has_wholesale_plugin = true;
            break;
        }
    }
}

if ($has_wholesale_plugin) {
    echo "✓ Wholesale plugin appears to be active (wholesale meta fields found)\n";
} else {
    echo "? Wholesale plugin status unclear\n";
}

echo "\n5. Troubleshooting recommendations...\n";

echo "If prices are not showing on frontend, try these steps:\n\n";

echo "A. Cache Issues:\n";
echo "   - Clear WordPress cache (if using caching plugins)\n";
echo "   - Clear browser cache\n";
echo "   - Check if CDN cache needs clearing\n\n";

echo "B. Wholesale Plugin Settings:\n";
echo "   - Verify wholesale user roles are properly configured\n";
echo "   - Check if you're logged in as a wholesale user\n";
echo "   - Ensure wholesale pricing display is enabled in plugin settings\n\n";

echo "C. Theme Compatibility:\n";
echo "   - Check if theme supports wholesale pricing display\n";
echo "   - Verify variation pricing hooks are working\n";
echo "   - Test with default theme to isolate theme issues\n\n";

echo "D. Plugin Conflicts:\n";
echo "   - Temporarily disable other pricing/discount plugins\n";
echo "   - Check for JavaScript errors in browser console\n";
echo "   - Verify WooCommerce is up to date\n\n";

echo "E. Database/API Issues:\n";
echo "   - Check WordPress error logs\n";
echo "   - Verify API credentials are correct\n";
echo "   - Test with WordPress admin user\n\n";

// Test if we can trigger a cache refresh
echo "6. Attempting to trigger cache refresh...\n";

// Update the product's modified date to potentially trigger cache refresh
$cache_refresh_data = [
    'date_modified_gmt' => gmdate('Y-m-d\TH:i:s')
];

$refresh_url = WP_URL . "products/$product_id?consumer_key=" . COMSUMER_KEY . "&consumer_secret=" . COMSUMER_SECRET;
$refresh_response = wp_curl_put($refresh_url, $cache_refresh_data);

if ($refresh_response) {
    echo "✓ Sent cache refresh signal to product\n";
} else {
    echo "✗ Failed to send cache refresh signal\n";
}

echo "\n=== Frontend Check Complete ===\n";
echo "\nNext steps:\n";
echo "1. Run this debug script: php debug_price_verification.php\n";
echo "2. Check your WordPress admin panel for the product variations\n";
echo "3. Test with a wholesale user account on the frontend\n";
echo "4. Check browser developer tools for any JavaScript errors\n";

?>
