<?php
/**
 * BigCommerce Price List Configuration
 * 
 * Configure your existing BigCommerce price list IDs here.
 * These price lists should already be created and assigned to customer groups.
 */

// BigCommerce Price List IDs (update these with your actual price list IDs)
define('BC_WHOLESALE_PRICE_LIST_ID', 2);        // Your Wholesale price list ID
define('BC_CANADA_WHOLESALE_PRICE_LIST_ID', 3); // Your Canada Wholesale price list ID

// WordPress Role ID mapping (these should not change)
define('WP_WHOLESALE_ROLE_ID', 453);        // WordPress Wholesale role ID
define('WP_CANADA_WHOLESALE_ROLE_ID', 455); // WordPress Canada Wholesale role ID

// Price list mapping for easy reference
$PRICE_LIST_MAPPING = [
    'wholesale' => [
        'bc_price_list_id' => BC_WHOLESALE_PRICE_LIST_ID,
        'wp_role_id' => WP_WHOLESALE_ROLE_ID,
        'name' => 'Wholesale'
    ],
    'canada_wholesale' => [
        'bc_price_list_id' => BC_CANADA_WHOLESALE_PRICE_LIST_ID,
        'wp_role_id' => WP_CANADA_WHOLESALE_ROLE_ID,
        'name' => 'Canada Wholesale'
    ]
];

/**
 * Get BigCommerce price list ID for a given type
 * 
 * @param string $type 'wholesale' or 'canada_wholesale'
 * @return int|false Price list ID or false if not found
 */
function getBigCommercePriceListId($type) {
    global $PRICE_LIST_MAPPING;
    
    if (isset($PRICE_LIST_MAPPING[$type])) {
        return $PRICE_LIST_MAPPING[$type]['bc_price_list_id'];
    }
    
    return false;
}

/**
 * Get WordPress role ID for a given type
 * 
 * @param string $type 'wholesale' or 'canada_wholesale'
 * @return int|false Role ID or false if not found
 */
function getWordPressRoleId($type) {
    global $PRICE_LIST_MAPPING;
    
    if (isset($PRICE_LIST_MAPPING[$type])) {
        return $PRICE_LIST_MAPPING[$type]['wp_role_id'];
    }
    
    return false;
}

/**
 * Get all configured price list mappings
 * 
 * @return array Price list mappings
 */
function getAllPriceListMappings() {
    global $PRICE_LIST_MAPPING;
    return $PRICE_LIST_MAPPING;
}

?>
